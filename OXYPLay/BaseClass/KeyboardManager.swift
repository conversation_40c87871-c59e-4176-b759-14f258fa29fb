//
//  KeyboardManager.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/30.
//

import UIKit
import Combine

/// 键盘管理器 - 自动处理键盘遮挡问题
class KeyboardManager: NSObject {

    // MARK: - 单例
    static let shared = KeyboardManager()

    // MARK: - 属性

    /// 是否启用键盘管理
    var isEnabled: Bool = true

    /// 键盘与输入框的最小距离
    var keyboardDistance: CGFloat = 15

    /// 是否允许点击空白区域收起键盘
    var resignOnTouchOutside: Bool = true

    /// 是否启用自动工具栏
    var enableAutoToolbar: Bool = true

    // MARK: - 私有属性

    /// 当前活跃的文本输入视图
    private weak var activeTextInputView: UIView?

    /// 当前键盘高度
    private var keyboardHeight: CGFloat = 0

    /// 是否是第一次弹出键盘
    private var isFirstKeyboardShow: Bool = true

    /// 原始状态记录
    private var originalContentOffset: CGPoint = .zero
    private var originalContentInset: UIEdgeInsets = .zero
    private var originalScrollIndicatorInsets: UIEdgeInsets = .zero

    /// 订阅集合
    private var cancellables = Set<AnyCancellable>()

    /// 当前处理的ScrollView
    private weak var currentScrollView: UIScrollView?

    /// 当前处理的ViewController
    private weak var currentViewController: UIViewController?

    /// 是否已经调整过视图
    private var hasAdjustedView: Bool = false

    /// 当前调整的动画是否正在进行
    private var isAnimating: Bool = false

    // MARK: - 初始化

    private override init() {
        super.init()
        setupNotifications()
    }

    deinit {
        stopObserving()
    }

    // MARK: - 公共方法

    /// 手动收起键盘
    func resignFirstResponder() {
        activeTextInputView?.resignFirstResponder()
    }

    /// 强制恢复视图到原始状态
    func forceRestoreViews() {
        // 立即恢复，不使用动画
        if let scrollView = currentScrollView {
            scrollView.contentInset = originalContentInset
            scrollView.scrollIndicatorInsets = originalScrollIndicatorInsets
            scrollView.contentOffset = originalContentOffset
        } else if let viewController = currentViewController {
            viewController.view.transform = .identity
        }

        // 清理状态
        cleanupState()
    }

    /// 检查是否有视图需要恢复
    func hasViewsToRestore() -> Bool {
        return hasAdjustedView && (currentScrollView != nil || currentViewController != nil)
    }

    // MARK: - 私有方法

    /// 停止监听键盘事件
    private func stopObserving() {
        cancellables.removeAll()
    }

    /// 设置通知监听
    private func setupNotifications() {
        cancellables.removeAll()

        // 键盘将要显示
        NotificationCenter.default.publisher(for: UIResponder.keyboardWillShowNotification)
            .sink { [weak self] notification in
                self?.keyboardWillShow(notification)
            }
            .store(in: &cancellables)

        // 键盘将要隐藏
        NotificationCenter.default.publisher(for: UIResponder.keyboardWillHideNotification)
            .sink { [weak self] notification in
                self?.keyboardWillHide(notification)
            }
            .store(in: &cancellables)

        // 文本框开始编辑
        NotificationCenter.default.publisher(for: UITextField.textDidBeginEditingNotification)
            .sink { [weak self] notification in
                self?.textInputDidBeginEditing(notification)
            }
            .store(in: &cancellables)

        NotificationCenter.default.publisher(for: UITextView.textDidBeginEditingNotification)
            .sink { [weak self] notification in
                self?.textInputDidBeginEditing(notification)
            }
            .store(in: &cancellables)

        // 文本框结束编辑
        NotificationCenter.default.publisher(for: UITextField.textDidEndEditingNotification)
            .sink { [weak self] notification in
                self?.textInputDidEndEditing(notification)
            }
            .store(in: &cancellables)

        NotificationCenter.default.publisher(for: UITextView.textDidEndEditingNotification)
            .sink { [weak self] notification in
                self?.textInputDidEndEditing(notification)
            }
            .store(in: &cancellables)

        // 应用生命周期通知
        NotificationCenter.default.publisher(for: UIApplication.didEnterBackgroundNotification)
            .sink { [weak self] _ in
                self?.isFirstKeyboardShow = true
                self?.forceRestoreViews()
            }
            .store(in: &cancellables)

        NotificationCenter.default.publisher(for: UIApplication.willEnterForegroundNotification)
            .sink { [weak self] _ in
                self?.isFirstKeyboardShow = true
                self?.cleanupState()
            }
            .store(in: &cancellables)

        // 监听应用即将失去焦点
        NotificationCenter.default.publisher(for: UIApplication.willResignActiveNotification)
            .sink { [weak self] _ in
                if self?.hasViewsToRestore() == true {
                    self?.forceRestoreViews()
                }
            }
            .store(in: &cancellables)
    }

    /// 键盘将要显示
    private func keyboardWillShow(_ notification: Notification) {
        guard isEnabled,
              let keyboardFrame = notification.userInfo?[UIResponder.keyboardFrameEndUserInfoKey] as? CGRect,
              let duration = notification.userInfo?[UIResponder.keyboardAnimationDurationUserInfoKey] as? Double,
              let curve = notification.userInfo?[UIResponder.keyboardAnimationCurveUserInfoKey] as? UInt else {
            return
        }

        keyboardHeight = keyboardFrame.height

        guard let activeView = activeTextInputView else { return }

        // 查找容器
        findContainers(for: activeView)

        // 第一次弹出键盘需要延迟处理，确保布局完成
        let delay: TimeInterval = isFirstKeyboardShow ? 0.1 : 0.0
        if isFirstKeyboardShow {
            isFirstKeyboardShow = false
        }

        DispatchQueue.main.asyncAfter(deadline: .now() + delay) { [weak self] in
            self?.adjustForKeyboard(duration: duration, curve: curve)
        }
    }

    /// 键盘将要隐藏
    private func keyboardWillHide(_ notification: Notification) {
        guard isEnabled else { return }

        let duration = notification.userInfo?[UIResponder.keyboardAnimationDurationUserInfoKey] as? Double ?? 0.25
        let curve = notification.userInfo?[UIResponder.keyboardAnimationCurveUserInfoKey] as? UInt ?? UInt(UIView.AnimationCurve.easeInOut.rawValue)

        keyboardHeight = 0
        restoreViews(duration: duration, curve: curve)
    }

    /// 文本输入开始编辑
    private func textInputDidBeginEditing(_ notification: Notification) {
        guard isEnabled, let textInput = notification.object as? UIView else { return }

        // 如果是同一个输入框，不需要重新处理
        if activeTextInputView == textInput {
            return
        }

        // 如果之前有活跃的输入框，先清理状态
        if activeTextInputView != nil && activeTextInputView != textInput {
            cleanupPreviousState()
        }

        activeTextInputView = textInput

        // 添加工具栏
        if enableAutoToolbar {
            addToolbar(to: textInput)
        }

        // 添加点击手势
        if resignOnTouchOutside {
            addTapGesture()
        }
    }

    /// 文本输入结束编辑
    private func textInputDidEndEditing(_ notification: Notification) {
        guard isEnabled else { return }

        removeTapGesture()

        if activeTextInputView == notification.object as? UIView {
            activeTextInputView = nil

            // 延迟检查是否需要恢复视图（防止键盘隐藏通知丢失）
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
                guard let self = self else { return }

                // 只有在键盘高度为0、视图还没有恢复、且没有正在动画时才强制恢复
                if self.keyboardHeight == 0 && self.hasViewsToRestore() && !self.isAnimating {
                    self.forceRestoreViews()
                }
            }
        }
    }

    // MARK: - 核心调整方法

    /// 查找容器
    private func findContainers(for textInputView: UIView) {
        // 重置状态
        currentScrollView = nil
        currentViewController = nil

        // 查找ViewController - 优化查找逻辑，支持弹窗controller
        var responder: UIResponder? = textInputView
        while responder != nil {
            if let viewController = responder as? UIViewController {
                // 检查是否是有效的ViewController
                if viewController.view.window != nil {
                    currentViewController = viewController
                    break
                }
            }
            responder = responder?.next
        }

        // 如果没有找到ViewController，尝试从window查找
        if currentViewController == nil, let window = textInputView.window {
            if let rootViewController = window.rootViewController {
                currentViewController = findTopViewController(from: rootViewController)
            }
        }

        // 查找ScrollView（查找最近的可滚动的ScrollView）
        var superview = textInputView.superview
        while superview != nil {
            if let scrollView = superview as? UIScrollView {
                // 检查ScrollView是否可以滚动
                if scrollView.isScrollEnabled {
                    currentScrollView = scrollView
                    // 保存原始状态（未被键盘调整过的状态）
                    if !hasAdjustedView {
                        originalContentInset = scrollView.contentInset
                        originalContentOffset = scrollView.contentOffset
                        originalScrollIndicatorInsets = scrollView.scrollIndicatorInsets
                    }
                    break
                }
            }
            superview = superview?.superview
        }
    }

    /// 查找最顶层的ViewController
    private func findTopViewController(from viewController: UIViewController) -> UIViewController {
        if let presentedViewController = viewController.presentedViewController {
            return findTopViewController(from: presentedViewController)
        }

        if let navigationController = viewController as? UINavigationController {
            return navigationController.topViewController ?? navigationController
        }

        if let tabBarController = viewController as? UITabBarController {
            return tabBarController.selectedViewController ?? tabBarController
        }

        return viewController
    }

    /// 调整视图以避免键盘遮挡
    private func adjustForKeyboard(duration: Double, curve: UInt) {
        guard let activeView = activeTextInputView,
              let window = activeView.window else { return }

        // 强制布局更新，确保获取正确的frame
        window.layoutIfNeeded()
        activeView.superview?.layoutIfNeeded()
        activeView.layoutIfNeeded()

        // 计算输入框在屏幕中的位置
        let textInputFrame = activeView.convert(activeView.bounds, to: window)
        let keyboardTop = window.bounds.height - keyboardHeight
        let textInputBottom = textInputFrame.maxY

        // 检查是否需要调整
        guard textInputBottom + keyboardDistance > keyboardTop else { return }

        let overlap = textInputBottom + keyboardDistance - keyboardTop

        if let scrollView = currentScrollView {
            adjustScrollView(scrollView, overlap: overlap, duration: duration, curve: curve)
        } else if let viewController = currentViewController {
            adjustViewController(viewController, overlap: overlap, duration: duration, curve: curve)
        }
    }

    /// 调整ScrollView
    private func adjustScrollView(_ scrollView: UIScrollView, overlap: CGFloat, duration: Double, curve: UInt) {
        guard let activeView = activeTextInputView,
              let window = scrollView.window,
              !isAnimating else { return }

        isAnimating = true

        // 强制布局更新
        scrollView.layoutIfNeeded()

        // 计算ScrollView在屏幕中的位置
        let scrollViewFrame = scrollView.convert(scrollView.bounds, to: window)
        let keyboardTop = window.bounds.height - keyboardHeight

        // 计算键盘与ScrollView的重叠部分
        let scrollViewBottom = scrollViewFrame.maxY
        let keyboardOverlap = max(0, scrollViewBottom - keyboardTop)

        // 只有当键盘确实遮挡了ScrollView时才调整
        guard keyboardOverlap > 0 else {
            isAnimating = false
            return
        }

        // 调整contentInset
        var newContentInset = originalContentInset
        newContentInset.bottom = originalContentInset.bottom + keyboardOverlap

        // 计算输入框在ScrollView中的位置
        let textInputFrame = activeView.convert(activeView.bounds, to: scrollView)

        // 计算可见区域高度（减去键盘遮挡部分）
        let visibleHeight = scrollView.bounds.height - keyboardOverlap

        // 计算输入框底部相对于ScrollView顶部的位置
        let textInputBottomInScrollView = textInputFrame.maxY

        // 计算当前可见区域的底部位置
        let currentVisibleBottom = scrollView.contentOffset.y + visibleHeight

        // 如果输入框底部超出了可见区域，需要滚动
        var newOffsetY = scrollView.contentOffset.y
        if textInputBottomInScrollView + keyboardDistance > currentVisibleBottom {
            newOffsetY = textInputBottomInScrollView + keyboardDistance - visibleHeight
        }

        // 限制在有效范围内
        let maxOffsetY = max(0, scrollView.contentSize.height - scrollView.bounds.height + newContentInset.bottom)
        newOffsetY = max(0, min(newOffsetY, maxOffsetY))

        // 执行动画
        UIView.animate(withDuration: duration,
                      delay: 0,
                      options: UIView.AnimationOptions(rawValue: curve << 16)) { [weak self] in
            guard let self = self else { return }
            scrollView.contentInset = newContentInset
            scrollView.scrollIndicatorInsets = newContentInset
            scrollView.contentOffset = CGPoint(x: scrollView.contentOffset.x, y: newOffsetY)
        } completion: { [weak self] _ in
            self?.isAnimating = false
            self?.hasAdjustedView = true
        }
    }

    /// 调整ViewController
    private func adjustViewController(_ viewController: UIViewController, overlap: CGFloat, duration: Double, curve: UInt) {
        guard let activeView = activeTextInputView,
              let window = activeView.window,
              !isAnimating else { return }

        isAnimating = true

        let textInputFrame = activeView.convert(activeView.bounds, to: window)
        let keyboardTop = window.bounds.height - keyboardHeight
        let textInputBottom = textInputFrame.maxY + keyboardDistance

        // 只有当输入框被键盘遮挡时才调整
        if textInputBottom > keyboardTop {
            let moveDistance = textInputBottom - keyboardTop
            let maxUpwardMovement = textInputFrame.minY - viewController.view.safeAreaInsets.top - 20
            let actualMovement = min(moveDistance, maxUpwardMovement)

            UIView.animate(withDuration: duration,
                          delay: 0,
                          options: UIView.AnimationOptions(rawValue: curve << 16)) {
                viewController.view.transform = CGAffineTransform(translationX: 0, y: -actualMovement)
            } completion: { [weak self] _ in
                self?.isAnimating = false
                self?.hasAdjustedView = true
            }
        } else {
            isAnimating = false
        }
    }

    /// 恢复视图位置
    private func restoreViews(duration: Double, curve: UInt) {
        // 如果正在动画中，等待动画完成后再恢复
        if isAnimating {
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) { [weak self] in
                self?.restoreViews(duration: duration, curve: curve)
            }
            return
        }

        // 如果没有调整过视图，直接清理状态
        if !hasAdjustedView {
            cleanupState()
            return
        }

        isAnimating = true

        if let scrollView = currentScrollView {
            // 检查ScrollView是否仍然有效
            guard scrollView.superview != nil else {
                isAnimating = false
                cleanupState()
                return
            }

            UIView.animate(withDuration: duration,
                          delay: 0,
                          options: UIView.AnimationOptions(rawValue: curve << 16)) { [weak self] in
                guard let self = self else { return }

                // 恢复到真正的原始状态
                scrollView.contentInset = self.originalContentInset
                scrollView.scrollIndicatorInsets = self.originalScrollIndicatorInsets
                scrollView.contentOffset = self.originalContentOffset
            } completion: { [weak self] _ in
                self?.isAnimating = false
                self?.cleanupState()
            }
        } else if let viewController = currentViewController {
            UIView.animate(withDuration: duration,
                          delay: 0,
                          options: UIView.AnimationOptions(rawValue: curve << 16)) {
                viewController.view.transform = .identity
            } completion: { [weak self] _ in
                self?.isAnimating = false
                self?.cleanupState()
            }
        } else {
            isAnimating = false
            cleanupState()
        }
    }

    /// 清理之前的状态
    private func cleanupPreviousState() {
        if hasAdjustedView {
            // 立即恢复之前的状态，不使用动画
            if let scrollView = currentScrollView {
                scrollView.contentInset = originalContentInset
                scrollView.scrollIndicatorInsets = originalScrollIndicatorInsets
                scrollView.contentOffset = originalContentOffset
            } else if let viewController = currentViewController {
                viewController.view.transform = .identity
            }
        }
        cleanupState()
    }

    /// 清理状态
    private func cleanupState() {
        currentScrollView = nil
        currentViewController = nil
        hasAdjustedView = false
        isAnimating = false
    }


    // MARK: - 工具栏和手势处理

    /// 添加工具栏
    private func addToolbar(to textInput: UIView) {
        // 检查是否已经有工具栏
        if let textField = textInput as? UITextField, textField.inputAccessoryView != nil {
            return
        }
        if let textView = textInput as? UITextView, textView.inputAccessoryView != nil {
            return
        }

        let toolbar = createToolbar()

        if let textField = textInput as? UITextField {
            textField.inputAccessoryView = toolbar
        } else if let textView = textInput as? UITextView {
            textView.inputAccessoryView = toolbar
        }
    }

    /// 创建工具栏
    private func createToolbar() -> UIToolbar {
        let toolbar = UIToolbar()
        toolbar.frame = CGRect(x: 0, y: 0, width: UIScreen.main.bounds.width, height: 44)
        toolbar.backgroundColor = UIColor.systemBackground
        toolbar.tintColor = color_blue

        let flexSpace = UIBarButtonItem(barButtonSystemItem: .flexibleSpace, target: nil, action: nil)
        let doneButton = UIBarButtonItem(barButtonSystemItem: .done, target: self, action: #selector(doneButtonTapped))

        toolbar.items = [flexSpace, doneButton]
        return toolbar
    }

    @objc private func doneButtonTapped() {
        activeTextInputView?.resignFirstResponder()
    }


    /// 添加点击手势
    private func addTapGesture() {
        guard let window = getKeyWindow() else { return }

        let existingGesture = window.gestureRecognizers?.first { gesture in
            return gesture is UITapGestureRecognizer && gesture.view == window
        }

        if existingGesture == nil {
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(windowTapped))
            tapGesture.cancelsTouchesInView = false
            tapGesture.delegate = self
            window.addGestureRecognizer(tapGesture)
        }
    }

    /// 移除点击手势
    private func removeTapGesture() {
        guard let window = getKeyWindow() else { return }

        window.gestureRecognizers?.forEach { gesture in
            if gesture is UITapGestureRecognizer {
                window.removeGestureRecognizer(gesture)
            }
        }
    }

    @objc private func windowTapped(_ gesture: UITapGestureRecognizer) {
        guard resignOnTouchOutside else { return }

        let location = gesture.location(in: gesture.view)

        // 检查点击位置是否在文本输入框内
        if let activeView = activeTextInputView,
           let window = activeView.window {
            let activeViewFrame = activeView.convert(activeView.bounds, to: window)
            if activeViewFrame.contains(location) {
                return
            }
        }

        activeTextInputView?.resignFirstResponder()

        // 延迟检查是否需要强制恢复视图
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) { [weak self] in
            guard let self = self else { return }

            // 如果视图还没有恢复且没有正在动画，强制恢复
            if self.hasViewsToRestore() && !self.isAnimating {
                self.forceRestoreViews()
            }
        }
    }

    /// 获取当前的keyWindow
    private func getKeyWindow() -> UIWindow? {
        if #available(iOS 15.0, *) {
            return UIApplication.shared
                .connectedScenes
                .compactMap { $0 as? UIWindowScene }
                .first(where: { $0.activationState == .foregroundActive })?
                .windows
                .first(where: \.isKeyWindow)
        } else if #available(iOS 13.0, *) {
            return UIApplication.shared.windows.first(where: \.isKeyWindow)
        } else {
            return UIApplication.shared.keyWindow
        }
    }
}

// MARK: - UIGestureRecognizerDelegate

extension KeyboardManager: UIGestureRecognizerDelegate {

    func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldRecognizeSimultaneouslyWith otherGestureRecognizer: UIGestureRecognizer) -> Bool {
        return true
    }

    func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldReceive touch: UITouch) -> Bool {
        if touch.view is UIButton || touch.view is UIControl {
            return false
        }
        return true
    }
}

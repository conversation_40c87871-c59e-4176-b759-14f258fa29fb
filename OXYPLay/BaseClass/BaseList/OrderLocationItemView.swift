//
//  OrderLocationItemView.swift
//  OXYPLay
//
//  Created by Renh<PERSON> on 2025/7/17.
//

class OrderLocationItemView: ListSelectItemView {

    // MARK: - UI组件

    lazy var iconImageView = UIImageView().then {
        $0.image = UIImage(named: "baselist_location")
    }

    lazy var titleLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 14,weight: .medium)
        $0.textColor = color_2B2C2F
        $0.setContentHuggingPriority(.required, for: .horizontal)
    }

    lazy var valueLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 12,weight: .regular)
        $0.textColor = UIColor(hexString: "2B2C2F", transparency: 1)
    }
    lazy var placeholderLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 12,weight: .regular)
        $0.textColor = UIColor(hexString: "2B2C2F", transparency: 0.56)
    }
   
    lazy var arrowImageView = UIImageView().then {
        $0.image = UIImage(named: "baselist_arrow")
    }
  
    // MARK: - 初始化方法

    override func configUI() {
        backgroundColor = .white
        addSubview(iconImageView)
        addSubview(titleLabel)
        addSubview(valueLabel)
        addSubview(arrowImageView)
        addSubview(placeholderLabel)

    }

    override func configLayout() {
       
        iconImageView.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(20)
        }

        titleLabel.snp.makeConstraints { make in
            make.left.equalTo(iconImageView.snp.right).offset(10)
            make.right.lessThanOrEqualTo(arrowImageView.snp.left).offset(-12)
            make.top.equalTo(12)
        }
        placeholderLabel.snp.makeConstraints { make in
            make.left.equalTo(titleLabel)
            make.top.equalTo(titleLabel.snp.bottom).offset(10)
            make.right.lessThanOrEqualTo(arrowImageView.snp.left).offset(-12)

        }
        valueLabel.snp.makeConstraints { make in
            make.left.equalTo(titleLabel)
            make.top.equalTo(placeholderLabel.snp.bottom).offset(10)
            make.right.lessThanOrEqualTo(arrowImageView.snp.left).offset(-12)
            make.bottom.equalTo(-12)
        }
        arrowImageView.snp.makeConstraints { make in
            make.right.equalTo(-12)
            make.centerY.equalToSuperview()
            make.width.equalTo(14)
            make.height.equalTo(14)
        }
    }
    override func configure(with config: ListItemConfig) {
        super.configure(with: config)
        guard let model = config.data as? ProductAddressItemModel else{
            return
        }
        titleLabel.text = model.recipient_name + " 86-" + model.phone.maskPhoneNumber
        placeholderLabel.text = model.region
        valueLabel.text = model.detail
   
    }
}


//
//  OrderService.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/24.
//

import Foundation
import Moya

/// 订单API服务
/// 提供订单相关的所有接口操作，包括创建订单、立即购买、取消订单等
enum OrderService {
    /// 购物车创建订单
    /// - Parameter params: 包含cart_item_ids、address_id、payment_type等参数
    case create(params: RequestParametersConvertible)
    
    /// 创建服务订单
    /// - Parameter params: 包含service_id、service_type、address_id等参数
    case createService(params: RequestParametersConvertible)
    
    /// 商品立即购买
    /// - Parameter params: 包含product_id、sku_id、quantity、address_id等参数
    case buyNow(params: RequestParametersConvertible)
    
    /// 取消订单
    /// - Parameter params: 包含order_id、reason等参数
    case cancel(params: RequestParametersConvertible)
    
    /// 订单详情（买家视角）
    /// - Parameter params: 包含order_id参数
    case orderDetail(params: RequestParametersConvertible)

    /// 订单支付接口
    /// - Parameter params: 包含order_id、pay_method参数
    case pay(params: RequestParametersConvertible)

    /// 支付成功通知接口
    /// - Parameter params: 包含order_id、pay_method、trade_no等参数
    case paymentNotify(params: RequestParametersConvertible)

    /// 申请退款接口
    /// - Parameter params: 包含order_id、order_item_ids、refund_type、refund_reason、refund_remark等参数
    case applyRefund(params: RequestParametersConvertible)

    /// 卖家同意退款申请接口
    /// - Parameter params: 包含order_id、address_id（可选）、address_snapshot（可选）等参数
    case agreeRefund(params: RequestParametersConvertible)

    /// 买家取消退款申请接口
    /// - Parameter params: 包含orderId参数
    case cancelRefund(params: RequestParametersConvertible)

    /// 卖家拒绝退款申请接口
    /// - Parameter params: 包含orderId、reject_reason（可选）参数
    case rejectRefund(params: RequestParametersConvertible)

    /// 卖家拒绝收货（终止退货流程）接口
    /// - Parameter params: 包含orderId、remark（可选）参数
    case rejectReturn(params: RequestParametersConvertible)

    /// 卖家确认收货并退款接口
    /// - Parameter params: 包含orderId参数
    case confirmReturnRefund(params: RequestParametersConvertible)
}

extension OrderService: GeneralAPIService {
    var path: String {
        switch self {
        case .create:
            return APIConstants.OrderApi + "/create"
        case .createService:
            return APIConstants.OrderApi + "/create-service-order"
        case .buyNow:
            return APIConstants.OrderApi + "/buy-now"
        case .cancel:
            return APIConstants.OrderApi + "/cancel-order"
        case .orderDetail:
            return APIConstants.OrderApi + "/order-detail"
        case .pay:
            return APIConstants.PayApi + "/pay"
        case .paymentNotify:
            return APIConstants.OrderApi + "/payment-notify"
        case .applyRefund:
            return APIConstants.OrderApi + "/apply-refund"
        case .agreeRefund:
            return APIConstants.OrderApi + "/agree-refund"
        case .cancelRefund:
            return APIConstants.OrderApi + "/cancel-refund"
        case .rejectRefund:
            return APIConstants.OrderApi + "/reject-refund"
        case .rejectReturn:
            return APIConstants.OrderApi + "/reject-return"
        case .confirmReturnRefund:
            return APIConstants.OrderApi + "/confirm-return-refund"
        }
    }
    
    var task: Task {
        switch self {
        case let .create(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .createService(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .buyNow(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .cancel(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .orderDetail(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .pay(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .paymentNotify(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .applyRefund(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .agreeRefund(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .cancelRefund(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .rejectRefund(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .rejectReturn(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        case let .confirmReturnRefund(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        }
    }
}

//
//  AppDelegate.swift
//  OXYPLay
//
//  Created by Renhw on 2025/5/22.
//

import UIKit
import IQKeyboardManagerSwift
@main
class AppDelegate: UIResponder, UIApplicationDelegate {
    var window: UIWindow?
    private let appManager = AppManager.shared
    
    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> <PERSON><PERSON> {
        // 使用AppManager初始化应用
        appManager.setupApplication(application, didFinishLaunchingWithOptions: launchOptions)

        // 启用自定义键盘管理器
        KeyboardManager.shared.isEnabled = true
        KeyboardManager.shared.resignOnTouchOutside = true
        KeyboardManager.shared.enableAutoToolbar = false
        // 初始化窗口
        window = UIWindow(frame: UIScreen.main.bounds)
        LoginManager.shared.setup(with: window)
        
        // 设置根视图控制器为自定义TabBarController
        let tabBarController = MyTabBarController()
        window?.rootViewController = tabBarController
        window?.makeKeyAndVisible()
        return true
    }
    
    // 处理URL Scheme回调
    func application(_ app: UIApplication, open url: URL, options: [UIApplication.OpenURLOptionsKey: Any] = [:]) -> Bool {
        return appManager.handleOpenURL(app, open: url, options: options)
    }
    
    // 处理Universal Link
    func application(_: UIApplication, continue userActivity: NSUserActivity, restorationHandler _: @escaping ([UIUserActivityRestoring]?) -> Void) -> Bool {
        return appManager.handleUniversalLink(userActivity)
    }

    // 应用即将进入前台
    func applicationWillEnterForeground(_ application: UIApplication) {
        appManager.handleApplicationWillEnterForeground()
    }
}

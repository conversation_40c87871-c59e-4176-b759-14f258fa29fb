//
//  LocationSelectViewModel.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/13.
//

import Foundation
import Combine



class LocationSelectViewModel: BaseViewModel {

    // MARK: - Published Properties

    /// 地区列表数据
    @Published var regionList: [RegionListItemModel] = []

    /// 地址列表数据
    @Published var productAddressModel: ProductAddressModel?

    /// 当前选中的省份
    @Published var selectedProvince: RegionListItemModel?

    /// 当前选中的城市
    @Published var selectedCity: RegionListItemModel?

    /// 当前选中的区县
    @Published var selectedDistrict: RegionListItemModel?

    /// 当前选中的街道
    @Published var selectedStreet: RegionListItemModel?

    // MARK: - Properties

    /// 地址类型
    var addressType: AddressType = .shipping

    // MARK: - Initialization

    override init() {
        super.init()
    }

    // MARK: - Public Methods

    // MARK: - 地区选择相关方法

    /// 获取地区列表
    /// - Parameter parent_code: 父级地区代码，nil表示获取省份列表
    func fetchRegionList(parent_code: String?) {
        requestState = .loading

        var params = RequestParameters(["": ""])
        if let parent_code = parent_code {
            params = RequestParameters([
                "parent_code": parent_code,
            ])
        }

        requestModel(CommentService.regionList(params: params), type: [RegionListItemModel].self)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] completion in
                guard let self = self else { return }

                switch completion {
                case .finished:
                    self.requestState = .success
                case .failure(let error):
                    self.requestState = .failure(self.handleError(error))
                    self.regionList = []
                }
            } receiveValue: { [weak self] models in
                guard let self = self else { return }
                self.regionList = models
            }
            .store(in: &cancellables)
    }

    /// 设置选中的省份
    /// - Parameter province: 省份模型
    func setSelectedProvince(_ province: RegionListItemModel) {
        selectedProvince = province
        selectedCity = nil
        selectedDistrict = nil
        selectedStreet = nil
    }

    /// 设置选中的城市
    /// - Parameter city: 城市模型
    func setSelectedCity(_ city: RegionListItemModel) {
        selectedCity = city
        selectedDistrict = nil
        selectedStreet = nil
    }

    /// 设置选中的区县
    /// - Parameter district: 区县模型
    func setSelectedDistrict(_ district: RegionListItemModel) {
        selectedDistrict = district
        selectedStreet = nil
    }

    /// 设置选中的街道
    /// - Parameter street: 街道模型
    func setSelectedStreet(_ street: RegionListItemModel) {
        selectedStreet = street
    }

    /// 获取完整的地址字符串
    /// - Returns: 完整地址
    func getFullAddress() -> String {
        var address = ""

        if let province = selectedProvince {
            address += province.name
        }

        if let city = selectedCity, city.name != selectedProvince?.name {
            address += city.name
        }

        if let district = selectedDistrict {
            address += district.name
        }

        if let street = selectedStreet {
            address += street.name
        }

        return address
    }

    /// 重置选择状态
    func resetSelection() {
        selectedProvince = nil
        selectedCity = nil
        selectedDistrict = nil
        selectedStreet = nil
        regionList = []
    }

    // MARK: - 地址列表相关方法

    /// 获取地址列表
    /// - Parameter type: 地址类型
    func fetchAddressList(type: AddressType? = nil) {
        let addressType = type ?? self.addressType
        let params = RequestParameters([
            "type": "\(addressType.rawValue)"
        ])

        requestModel(MineService.addressList(params: params), type: ProductAddressModel.self)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] completion in
                if case .failure(let error) = completion {
                    print("获取地址列表失败: \(error.localizedDescription)")
                }
            } receiveValue: { [weak self] models in
                // 只在没有选中状态时设置默认选择
                var mutableModels = models
                let processedModels = mutableModels.setDefaultSelectionIfNeeded()
                self?.productAddressModel = processedModels
            }
            .store(in: &cancellables)
    }

    /// 获取收货地址列表
    func fetchShippingAddressList() {
        fetchAddressList(type: .shipping)
    }

    /// 获取退货地址列表
    func fetchRefundAddressList() {
        fetchAddressList(type: .refund)
    }

    /// 设置默认地址
    /// - Parameters:
    ///   - addressId: 地址ID
    ///   - completion: 完成回调
    func setDefaultAddress(addressId: String, completion: @escaping (Bool) -> Void) {
        let params = RequestParameters([
            "address_id": addressId,
            "type": "\(addressType.rawValue)"
        ])

        request(MineService.setDefaultAddress(params: params))
            .receive(on: DispatchQueue.main)
            .sink { completionResult in
                switch completionResult {
                case .failure:
                    completion(false)
                case .finished:
                    break
                }
            } receiveValue: { [weak self] response in
                if response.code == 200 {
                    // 设置成功后重新获取地址列表
                    self?.fetchAddressList()
                    completion(true)
                } else {
                    completion(false)
                }
            }
            .store(in: &cancellables)
    }

    // MARK: - 地址创建和更新方法

    /// 创建新地址
    /// - Parameters:
    ///   - addressData: 地址数据
    ///   - completion: 完成回调
    func createAddress(addressData: [String: Any], completion: @escaping (Bool) -> Void) {
        let params = RequestParameters(addressData)

        requestModel(MineService.createAddress(params: params), type: CreateAddressResponse.self)
            .receive(on: DispatchQueue.main)
            .sink { completionResult in
                switch completionResult {
                case .failure:
                    completion(false)
                case .finished:
                    break
                }
            } receiveValue: { [weak self] response in
              
                completion(true)
            }
            .store(in: &cancellables)
    }

    /// 更新地址
    /// - Parameters:
    ///   - addressData: 地址数据
    ///   - completion: 完成回调
    func updateAddress(addressData: [String: Any], completion: @escaping (Bool) -> Void) {
        let params = RequestParameters(addressData)

        request(MineService.updateAddress(params: params))
            .receive(on: DispatchQueue.main)
            .sink { completionResult in
                switch completionResult {
                case .failure:
                    completion(false)
                case .finished:
                    break
                }
            } receiveValue: { [weak self] response in
                if response.code == 200 {
                    
                    completion(true)
                } else {
                    completion(false)
                }
            }
            .store(in: &cancellables)
    }

    /// 删除地址
    /// - Parameters:
    ///   - addressId: 地址ID
    ///   - completion: 完成回调
    func deleteAddress(addressId: String, completion: @escaping (Bool) -> Void) {
        let params = RequestParameters([
            "address_id": addressId
        ])

        request(MineService.deleteAddress(params: params))
            .receive(on: DispatchQueue.main)
            .sink { completionResult in
                switch completionResult {
                case .failure:
                    completion(false)
                case .finished:
                    break
                }
            } receiveValue: { [weak self] response in
                if response.code == 200 {
                   
                    completion(true)
                } else {
                    completion(false)
                }
            }
            .store(in: &cancellables)
    }

    // MARK: - Private Methods
    
    /// 处理网络错误
    /// - Parameter error: 网络错误
    /// - Returns: 错误描述
    private func handleError(_ error: NetworkError) -> String {
        switch error {
        case .networkError(let response):
            return response.message
        case .decodingError:
            return "数据解析错误"
        case .noConnection:
            return "网络连接失败"
        case .tokenExpired:
            return "登录已过期，请重新登录"
        case .tokenError:
            return "登录状态异常"
        }
    }
}

//
//  MineAddressCell.swift
//  OXYPLay
//
//  Created by Renhw on 2025/8/4.
//

import UIKit
import SnapKit
import Combine
import Then

/// 地址列表Cell
class MineAddressCell: UITableViewCell {

    // MARK: - Properties
    
    var editButtonTapped: ((ProductAddressItemModel) -> Void)?
    var deleteButtonTapped: ((ProductAddressItemModel) -> Void)?
    var defaultButtonTapped: ((ProductAddressItemModel) -> Void)?
    
    private var address: ProductAddressItemModel?
    private var cancellables = Set<AnyCancellable>()

    // MARK: - UI Components
    
    private lazy var containerView = UIView().then {
        $0.backgroundColor = .white
        $0.layer.cornerRadius = 16
        $0.layer.borderWidth = 0.5
        $0.layer.borderColor = color_blue.cgColor
        $0.masksToBounds = true
    }
    private lazy var adressView = UIView()
    private lazy var stackContentView = UIStackView().then {
        $0.axis = .vertical
        $0.spacing = 0
    }
    private lazy var defaultLabel = BasePaddingLabel().then {
        $0.text = "默认"
        $0.font = UIFont.systemFont(ofSize: 11,weight: .regular)
        $0.layer.borderColor = UIColor(hexString: "#FF7800")?.cgColor
        $0.layer.borderWidth = 1
        $0.textColor = UIColor(hexString: "#FF7800")
        $0.textAlignment = .center
        $0.layer.cornerRadius = 4
        $0.layer.masksToBounds = true
        $0.isHidden = true
    }
    private lazy var nameLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        $0.textColor = color_2B2C2F
    }
    private lazy var phoneLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 12, weight: .regular)
        $0.textColor = color_2B2C2F
    }
    private lazy var editButton = UIButton(type: .custom).then {
        $0.setImage(UIImage(systemName: "pencil"), for: .normal)
        $0.tintColor = color_999DA1
    }
    
    private lazy var addressLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 12)
        $0.textColor = UIColor(hexString: "#2B2C2F", transparency: 0.56)
        $0.numberOfLines = 0
    }
    
    private lazy var detailLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 12)
        $0.textColor = UIColor(hexString: "#2B2C2F", transparency: 1)
        $0.numberOfLines = 0
    }
    private lazy var managerContentView = UIView()

    private lazy var managerLineView = UIView().then{
        $0.backgroundColor = UIColor(hexString: "2B2C2F", transparency: 0.08)
    }

    // 管理模式下的控件
    private lazy var defaultButton = BaseButton().then {
        $0.setImage(UIImage(named: "baselist_single_normal"), for: .normal)
        $0.setImage(UIImage(named: "baselist_single_select"), for: .selected)
        $0.setTitle("默认收货地址", for: .normal)
        $0.setTitleColor(color_2B2C2F, for: .normal)
        $0.titleLabel?.font = UIFont.systemFont(ofSize: 12)
    }
    
    private lazy var deleteButton = BaseButton().then {
        $0.setImage(UIImage(systemName: "trash"), for: .normal)
        $0.setTitle("删除", for: .normal)
        $0.setTitleColor(color_999DA1, for: .normal)
        $0.titleLabel?.font = UIFont.systemFont(ofSize: 12)
        $0.tintColor = color_999DA1
    }

    // MARK: - Initialization
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
        setupBindings()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - Setup
    
    private func setupUI() {
        backgroundColor = color_F6F8F9
        selectionStyle = .none
        
        contentView.addSubview(containerView)
        containerView.addSubview(stackContentView)
        stackContentView.addArrangedSubview(adressView)
        stackContentView.addArrangedSubview(managerContentView)
        
        adressView.addSubview(defaultLabel)
        adressView.addSubview(nameLabel)
        adressView.addSubview(phoneLabel)
        adressView.addSubview(editButton)
        adressView.addSubview(addressLabel)
        adressView.addSubview(detailLabel)
        
        managerContentView.addSubview(managerLineView)
        managerContentView.addSubview(defaultButton)
        managerContentView.addSubview(deleteButton)
        
        setupConstraints()
    }
    
    private func setupConstraints() {
        
        containerView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(12)
            make.left.equalToSuperview().offset(12)
            make.right.equalToSuperview().offset(-12)
            make.bottom.equalToSuperview().offset(0)
        }
        
        stackContentView.snp.makeConstraints{make in
            make.top.equalToSuperview().offset(12)
            make.left.equalToSuperview().offset(12)
            make.right.equalToSuperview().offset(-12)
            make.bottom.equalToSuperview().offset(-12)
        }
        
        nameLabel.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.left.equalToSuperview()
            make.height.equalTo(19)

        }
        phoneLabel.snp.makeConstraints { make in
            make.centerY.equalTo(nameLabel)
            make.left.equalTo(nameLabel.snp.right).offset(6)
        }
        defaultLabel.snp.makeConstraints { make in
            make.centerY.equalTo(nameLabel)
            make.width.equalTo(38)
            make.height.equalTo(19)
            make.right.lessThanOrEqualTo(editButton.snp.left).offset(-8)
            make.left.equalTo(phoneLabel.snp.right).offset(6)
        }
        
        editButton.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.right.equalToSuperview()
            make.width.height.equalTo(20)
        }
        
        addressLabel.snp.makeConstraints { make in
            make.top.equalTo(nameLabel.snp.bottom).offset(10)
            make.left.equalTo(nameLabel)
            make.right.equalTo(editButton.snp.left).offset(-8)
        }
        
        detailLabel.snp.makeConstraints { make in
            make.top.equalTo(addressLabel.snp.bottom).offset(10)
            make.left.equalTo(nameLabel)
            make.right.equalTo(editButton.snp.left).offset(-8)
            make.bottom.equalTo(0)

        }
        
        managerLineView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.top.equalTo(12)
            make.height.equalTo(1)
        }
        
        
        defaultButton.snp.makeConstraints { make in
            make.top.equalTo(managerLineView.snp.bottom).offset(12)
            make.left.equalTo(0)
            make.bottom.equalTo(0)
        }
        
        deleteButton.snp.makeConstraints { make in
            make.centerY.equalTo(defaultButton)
            make.right.equalTo(0)
            make.bottom.equalTo(0)
        }
    }
    
    private func setupBindings() {
        editButton.tapPublisher
            .sink { [weak self] _ in
                guard let self = self, let address = self.address else { return }
                self.editButtonTapped?(address)
            }
            .store(in: &cancellables)
        
        deleteButton.tapPublisher
            .sink { [weak self] _ in
                guard let self = self, let address = self.address else { return }
                self.deleteButtonTapped?(address)
            }
            .store(in: &cancellables)
        
        defaultButton.tapPublisher
            .sink { [weak self] _ in
                guard let self = self, let address = self.address else { return }
                self.defaultButtonTapped?(address)
            }
            .store(in: &cancellables)
    }

    // MARK: - Configuration
    
    func configure(with address: ProductAddressItemModel, isManageMode: Bool) {
        self.address = address
        
        nameLabel.text = address.recipient_name
        phoneLabel.text = "86-\(address.phone.maskPhoneNumber)"
        addressLabel.text = address.region
        detailLabel.text = address.detail
        
        // 显示默认标签
        defaultLabel.isHidden = !address.is_default
        
        // 管理模式下的UI调整
        defaultButton.isSelected = address.is_default
        managerContentView.isHidden = !isManageMode
        if isManageMode{
            containerView.layer.borderWidth = 0
        }else{
            containerView.layer.borderWidth = address.isSelect ? 0.5:0
        }
   
    }
}

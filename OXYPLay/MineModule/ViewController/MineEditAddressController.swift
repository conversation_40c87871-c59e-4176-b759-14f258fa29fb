//
//  MineEditAddressController.swift
//  OXYPLay
//
//  Created by Renh<PERSON> on 2025/8/4.
//

import UIKit
import SnapKit
import Combine
import Then

/// 编辑地址控制器
class MineEditAddressController: BaseViewController {

    // MARK: - Properties

    private let addressType: AddressType
    private let isEditMode: Bool
    private var viewModel: LocationSelectViewModel?
    private var addressData: [String: Any] = [:]
    private var tempAddressData: [String: Any] = [:]
    // MARK: - UI Components
    

    private lazy var addressInfoView = UIView()
    
    private lazy var titleLabel = UILabel().then {
        $0.text = "地址信息"
        $0.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        $0.textColor = color_2B2C2F
    }
    
    private lazy var defaultButton = BaseButton().then {
        $0.titleLabel?.font = UIFont.systemFont(ofSize: 11, weight: .regular)
        $0.setTitle("默认收货", for: .normal)
        $0.setTitleColor(UIColor(hexString: "#2B2C2F"), for: .normal)
        $0.setImage(UIImage(named: "baselist_single_normal"), for: .normal)
        $0.setImage(UIImage(named: "baselist_single_select"), for: .selected)
    }
    
    private lazy var listView = BaseListView().then {
        $0.delegate = self
    }
    
    /// 保存按钮
    private lazy var toolBar = BaseTabToolBar().then {
        $0.delegate = self
        
    }

    // MARK: - Initialization
    
    init(addressType: AddressType, isEditMode: Bool) {
        self.addressType = addressType
        self.isEditMode = isEditMode
        super.init()
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        setupViewModel()
        configUI()
        configureListItems()
        setupBindings()
        setupNavigationBar()
    }

    // MARK: - UI Configuration

    override func configUI() {
        title = isEditMode ? "编辑地址" : "新增地址"
        toolBar.configureLeftMultipleRightFull(leftItems: [], rightButtonTitle: "确定")
        defaultButton.setTitle(addressType == .refund ? "默认退货":"默认收货", for: .normal)

        view.backgroundColor = color_F6F8F9

        view.addSubview(toolBar)
        view.addSubview(addressInfoView)
        view.addSubview(listView)
        addressInfoView.addSubview(titleLabel)
        addressInfoView.addSubview(defaultButton)
        setupConstraints()
    }
    
    private func setupConstraints() {
      
        
        addressInfoView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top).offset(12)
            make.left.equalToSuperview().offset(0)
            make.right.equalToSuperview().offset(-0)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(12)
            make.left.equalToSuperview().offset(12)
            make.bottom.equalTo(0)
        }
        
        defaultButton.snp.makeConstraints { make in
            make.centerY.equalTo(titleLabel)
            make.right.equalToSuperview().offset(-12)
        }
        
        listView.snp.makeConstraints { make in
            make.top.equalTo(addressInfoView.snp.bottom).offset(12)
            make.left.right.equalToSuperview()
            make.height.equalTo(44 * 4)
        }
        
        toolBar.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(ScreenInfo.totalTabBarHeight)
        }
    }

    override func setupBindings() {
        // 默认地址按钮
        defaultButton.tapPublisher
            .sink { [weak self] _ in
                self?.defaultButton.isSelected.toggle()
            }
            .store(in: &cancellables)
    }
    
    private func setupNavigationBar() {
        if isEditMode {
            // 编辑模式下添加删除按钮
            let deleteButton = UIButton(type: .system)
            deleteButton.setTitle("删除", for: .normal)
            deleteButton.titleLabel?.font = .systemFont(ofSize: 14, weight: .regular)
            deleteButton.setTitleColor(.red, for: .normal)
            deleteButton.addTarget(self, action: #selector(deleteButtonTapped), for: .touchUpInside)
            navigationItem.rightBarButtonItem = UIBarButtonItem(customView: deleteButton)
        }
    }

    // MARK: - Private Methods
    
    private func setupViewModel() {
        viewModel = LocationSelectViewModel()
    }
    
    private func configureListItems() {
        let items: [[ListItemConfig]] = [
            [
                ListItemConfig(type: .titleInput, identifier: "联系人", data: addressData["联系人"], isRequired: true, placeholder: "请输入联系人姓名", title: "联系人"),
                ListItemConfig(type: .titleInput, identifier: "手机号", data: addressData["手机号"], isRequired: true, placeholder: "请输入收货手机号", title: "手机号"),
                ListItemConfig(type: .starselect, identifier: "所在地区", data: addressData["所在地区"], isRequired: true, placeholder: addressType == .refund ? "请选择您的退货地址" : "请选择您的收货地址", title: "所在地区"),
                ListItemConfig(type: .titleInput, identifier: "详细地址", data: addressData["详细地址"], isRequired: true, placeholder: "请输入您的详细地址", title: "详细地址")
            ]
        ]
        listView.setItems(items)
    }
    
    func setAddressData(_ address: ProductAddressItemModel) {
        addressData = [
            "联系人": address.recipient_name,
            "手机号": address.phone,
            "所在地区": address.region,
            "详细地址": address.detail,
            "address_id": address.id,
            "province": address.province,
            "province_code": address.province_code,
            "city": address.city,
            "city_code": address.city_code,
            "district": address.district,
            "district_code": address.district_code,
            "street": address.street,
            "street_code": address.street_code
        ]
        defaultButton.isSelected = address.is_default
        
        // 重新配置列表项
        configureListItems()
    }
    
    private func saveAddress() {
        // 获取表单数据
        let formData = listView.getAllData()
        
        // 验证必填字段
        guard let recipientName = formData["联系人"] as? String, !recipientName.isEmpty,
              let phone = formData["手机号"] as? String, !phone.isEmpty,
              let region = formData["所在地区"] as? String, !region.isEmpty else {
            showErrorAlert("请填写完整的地址信息")
            return
        }
        
        // 验证地区相关的必传参数
        guard let province = addressData["province"] as? String, !province.isEmpty,
              let provinceCode = addressData["province_code"] as? String, !provinceCode.isEmpty,
              let city = addressData["city"] as? String, !city.isEmpty,
              let cityCode = addressData["city_code"] as? String, !cityCode.isEmpty,
              let district = addressData["district"] as? String, !district.isEmpty,
              let districtCode = addressData["district_code"] as? String, !districtCode.isEmpty,
              let street = addressData["street"] as? String, !street.isEmpty,
              let streetCode = addressData["street_code"] as? String, !streetCode.isEmpty else {
            showErrorAlert("请选择完整的地区信息")
            return
        }
        
        // 构建地址数据
        var params: [String: Any] = [
            "recipient_name": recipientName,
            "phone": phone,
            "province": province,
            "province_code": provinceCode,
            "city": city,
            "city_code": cityCode,
            "district": district,
            "district_code": districtCode,
            "street": street,
            "street_code": streetCode,
            "set_default": defaultButton.isSelected,
            "type": addressType.apiType
        ]
        
        // 添加详细地址
        if let detail = formData["详细地址"] as? String, !detail.isEmpty {
            params["detail"] = detail
        }
        
        // 如果是编辑模式，添加地址ID
        if isEditMode, let addressId = addressData["address_id"] {
            params["address_id"] = addressId
            updateAddress(params: params)
        } else {
            createAddress(params: params)
        }
    }
    
    private func createAddress(params: [String: Any]) {
        viewModel?.createAddress(addressData: params) { [weak self] success in
            DispatchQueue.main.async {
                if success {
                    self?.navigationController?.popViewController(animated: true)
                } else {
                    self?.showErrorAlert("地址创建失败")
                }
            }
        }
    }
    
    private func updateAddress(params: [String: Any]) {
        viewModel?.updateAddress(addressData: params) { [weak self] success in
            DispatchQueue.main.async {
                if success {
                    self?.navigationController?.popViewController(animated: true)
                } else {
                    self?.showErrorAlert("地址更新失败")
                }
            }
        }
    }
    
    @objc private func deleteButtonTapped() {
        let alertDialog = BaseAlertDialog()
        alertDialog.configure(
            title: "确定要删除这个地址吗？",
            leftButtonTitle: "确定",
            rightButtonTitle: "取消",
            showCloseButton: true,
            leftAction: { [weak self] in
                self?.performDeleteAddress()
            },
            rightAction: {
                // 取消删除
            }
        )
        alertDialog.show(in: self)
    }
    
    private func performDeleteAddress() {
        guard let addressId = addressData["address_id"] as? String else {
            showErrorAlert("地址ID不存在")
            return
        }

        viewModel?.deleteAddress(addressId: addressId) { [weak self] success in
            DispatchQueue.main.async {
                if success {
                    self?.navigationController?.popViewController(animated: true)
                } else {
                    self?.showErrorAlert("地址删除失败")
                }
            }
        }
    }

    private func handleLocationSelection() {
        // 保存当前的地址数据
        tempAddressData = listView.getAllData()
        tempAddressData["isDefault"] = defaultButton.isSelected

        // 跳转到地区选择页面
        let locationController = ProductSelectLocationController()

        // 监听地区选择完成事件
        locationController.locationSelectedPublisher
            .receive(on: DispatchQueue.main)
            .sink { [weak self] (province, city, district, street) in
                self?.handleLocationSelected(province: province, city: city, district: district, street: street)
            }
            .store(in: &cancellables)

        pushVc(locationController, animated: true)
    }

    private func handleLocationSelected(province: RegionListItemModel, city: RegionListItemModel?, district: RegionListItemModel?, street: RegionListItemModel?) {
        // 构建地区字符串
        var regionString = province.name
        if let city = city, city.name != province.name {
            regionString += city.name
        }
        if let district = district {
            regionString += district.name
        }
        if let street = street {
            regionString += street.name
        }

        // 更新地址数据中的地区信息
        addressData["所在地区"] = regionString
        addressData["province"] = province.name
        addressData["province_code"] = province.code

        if let city = city {
            addressData["city"] = city.name
            addressData["city_code"] = city.code
        }

        if let district = district {
            addressData["district"] = district.name
            addressData["district_code"] = district.code
        }

        if let street = street {
            addressData["street"] = street.name
            addressData["street_code"] = street.code
        }

        // 恢复其他表单数据
        for (key, value) in tempAddressData {
            if key != "所在地区" {
                addressData[key] = value
            }
        }

        // 重新配置列表项以更新UI
        configureListItems()
    }
}

// MARK: - BaseListViewDelegate

extension MineEditAddressController: BaseListViewDelegate {
    func listViewUpdate(_ listView: BaseListView, with data: Any?) {

    }

    func listViewClick(_ listView: BaseListView, config: ListItemConfig) {
        if config.identifier == "所在地区" {
            handleLocationSelection()
        }
    }

    func listViewValidate(_ listView: BaseListView, message: String) {
        showErrorAlert(message)
    }
}
extension MineEditAddressController: TabToolBarDelegate{
    func tabToolBar(_ toolBar: BaseTabToolBar, didClickRightButtonAt index: Int, item: ToolBarButtonItem?) {
        self.saveAddress()
    }
    
}

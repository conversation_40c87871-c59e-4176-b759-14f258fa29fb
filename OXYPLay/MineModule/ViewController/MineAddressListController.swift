//
//  MineAddressListController.swift
//  OXYPLay
//
//  Created by Renh<PERSON> on 2025/8/4.
//

import UIKit
import SnapKit
import Combine
import Then

/// 地址列表控制器（收货地址/退货地址）
class MineAddressListController: BaseViewController {

    // MARK: - Properties
    
    private let addressType: AddressType
    private var viewModel: LocationSelectViewModel?
    private var addressModel: ProductAddressModel?
    private var isManageMode: Bool = false

    // MARK: - UI Components
    
    private lazy var tableView = UITableView(frame: .zero, style: .plain).then {
        $0.backgroundColor = color_F6F8F9
        $0.separatorStyle = .none
        $0.showsVerticalScrollIndicator = false
        $0.delegate = self
        $0.dataSource = self
        $0.register(MineAddressCell.self, forCellReuseIdentifier: "MineAddressCell")
        $0.sectionHeaderHeight = 0.01
        $0.sectionFooterHeight = 0.01
        if #available(iOS 15.0, *) {
            $0.sectionHeaderTopPadding = 0
        }
    }
    
    private lazy var toolBar = MineAddressToolBar()

    // MARK: - Initialization
    
    init(addressType: AddressType) {
        self.addressType = addressType
        super.init()
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        setupViewModel()
        configUI()
        setupBindings()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        loadData()
    }

    // MARK: - UI Configuration

    override func configUI() {
        title = addressType.title
        view.backgroundColor = color_F6F8F9
        view.addSubview(tableView)
        view.addSubview(toolBar)
        
        tableView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide)
            make.left.right.equalToSuperview()
            make.bottom.equalTo(toolBar.snp.top)
        }
        
        toolBar.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(ScreenInfo.totalTabBarHeight)
        }
    }

    override func setupBindings() {
        // 监听地址数据变化
        viewModel?.$productAddressModel
            .receive(on: DispatchQueue.main)
            .sink { [weak self] model in
                guard let self = self, let model = model else { return }
                self.addressModel = model
                self.tableView.reloadData()
            }
            .store(in: &cancellables)
        // 编辑按钮回调
        toolBar.editButtonTapped = { [weak self] isSelect in
            guard let self = self else { return }
            self.isManageMode = isSelect
            self.tableView.reloadData()
        }
        toolBar.addButtonTapped = { [weak self]  in
            guard let self = self else { return }
            self.addNewAddress()
        }
    }

    // MARK: - Private Methods
    
    private func setupViewModel() {
        viewModel = LocationSelectViewModel()
        viewModel?.addressType = addressType
    }
    
    private func loadData() {
        switch addressType {
        case .shipping:
            viewModel?.fetchShippingAddressList()
        case .refund:
            viewModel?.fetchRefundAddressList()
        }
    }
    
    private func addNewAddress() {
        let editController = MineEditAddressController(addressType: addressType, isEditMode: false)
        pushVc(editController, animated: true)
    }
    
    private func editAddress(_ address: ProductAddressItemModel) {
        let editController = MineEditAddressController(addressType: addressType, isEditMode: true)
        editController.setAddressData(address)
        pushVc(editController, animated: true)
    }
    
    private func deleteAddress(_ address: ProductAddressItemModel) {
        let alertDialog = BaseAlertDialog()
        alertDialog.configure(
            title: "确定要删除这个地址吗？",
            leftButtonTitle: "确定",
            rightButtonTitle: "取消",
            showCloseButton: true,
            leftAction: { [weak self] in
                self?.performDeleteAddress(address)
            },
            rightAction: {
                // 取消删除
            }
        )
        alertDialog.show(in: self)
    }
    
    private func performDeleteAddress(_ address: ProductAddressItemModel) {
        viewModel?.deleteAddress(addressId: address.id) { [weak self] success in
            DispatchQueue.main.async {
                if success {
                    self?.showSuccessAlert("地址删除成功")
                    self?.loadData()
                    // 删除成功后会自动刷新列表（在ViewModel中处理）
                } else {
                    self?.showErrorAlert("地址删除失败")
                }
            }
        }
    }
    
    private func setDefaultAddress(_ address: ProductAddressItemModel) {
        viewModel?.setDefaultAddress(addressId: address.id) { [weak self] (success: Bool) in
            DispatchQueue.main.async {
                if success {
                    print("设置默认地址成功")
                    self?.loadData()
                } else {
                    print("设置默认地址失败")
                }
            }
        }
    }
}

// MARK: - UITableViewDataSource

extension MineAddressListController: UITableViewDataSource,UITableViewDelegate {
    func numberOfSections(in tableView: UITableView) -> Int {
        return 1
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        guard let addressModel = addressModel else { return 0 }
        return addressModel.address_list.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "MineAddressCell", for: indexPath) as! MineAddressCell
        
        guard let addressModel = addressModel else { return cell }
        
        let address = addressModel.address_list[indexPath.row]
        cell.configure(with: address, isManageMode: isManageMode)
        
        // 编辑按钮回调
        cell.editButtonTapped = { [weak self] address in
            self?.editAddress(address)
        }
        
        // 删除按钮回调
        cell.deleteButtonTapped = { [weak self] address in
            self?.deleteAddress(address)
        }
        
        // 设置默认地址回调
        cell.defaultButtonTapped = { [weak self] address in
            self?.setDefaultAddress(address)
        }
        
        return cell
    }
}

class MineAddressToolBar:BaseView{
    var editButtonTapped: ((_ isSelect:Bool) -> Void)?
    var addButtonTapped: (() -> Void)?
    lazy var addButton = BaseGradientButton().then {
        $0.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        $0.setTitleColor(.white, for: .normal)
        $0.backgroundColor = color_blue
        $0.setTitle("新增地址", for: .normal)
        $0.isRounded = true
    }
    lazy var editButton = BaseButton().then {
        $0.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        $0.setTitleColor(.white, for: .normal)
        $0.backgroundColor = UIColor(hexString: "788092", transparency: 0.08)
        $0.setTitleColor(color_2B2C2F, for: .normal)
        $0.setTitle("管理", for: .normal)
        $0.setTitle("取消管理", for: .selected)
        $0.isRounded = true
    }
    override func configUI() {
        self.layer.cornerRadius = 16
        self.masksToBounds = true
        self.backgroundColor = .white
        self.layer.maskedCorners = [.layerMinXMinYCorner,.layerMaxXMinYCorner]
        addSubview(editButton)
        addSubview(addButton)
        setupBindings()
            
    }
    override func configLayout() {
        editButton.snp.makeConstraints { make in
            make.top.left.equalTo(12)
            make.height.equalTo(44)
            make.width.equalTo(112)
        }
        addButton.snp.makeConstraints { make in
            make.top.height.equalTo(editButton)
            make.left.equalTo(editButton.snp.right).offset(12)
            make.right.equalTo(-12)
        }
    }
    override func setupBindings() {
        editButton.tapPublisher
            .sink { [weak self] _ in
                guard let self = self  else { return }
                self.editButton.isSelected.toggle()
                guard let editButtonTapped = self.editButtonTapped else{
                    return
                }
                editButtonTapped(self.editButton.isSelected)
            }
            .store(in: &cancellables)
        addButton.tapPublisher
            .sink { [weak self] _ in
                guard let self = self else { return }
                guard let addButtonTapped = self.addButtonTapped else{
                    return
                }
                addButtonTapped()
            }
            .store(in: &cancellables)
    }
}

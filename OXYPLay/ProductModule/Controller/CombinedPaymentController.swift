//
//  CombinedPaymentController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/24.
//

import UIKit
import Combine

class CombinedPaymentController: BasePresentController {

    /// 可用余额
    var availableBalance: Float = 0.0

    // MARK: - 订单相关信息
    ///订单id
    var orderId = -1
    /// 订单总金额
    var totalAmount: Float = 0.0
    /// 支付ViewModel
    private var paymentViewModel: CombinedPaymentViewModel?

    /// 订单创建处理器 - 外部提供的订单创建逻辑
    var orderCreationHandler: (() -> AnyPublisher<Int, Error>)?

    /// 支付参数设置器 - 在创建订单前设置支付参数到外部ViewModel
    var paymentParametersSetter: ((PaymentData) -> Void)?

    // MARK: - UI Components

    /// 支付组合视图
    private lazy var paymentView = CombinedPaymentView().then {
        $0.delegate = self
    }

    // MARK: - Lifecycle


    override func viewDidLoad() {
        super.viewDidLoad()
        configUI()
        setupPaymentView()
        initializePaymentViewModel()
        fetchWalletBalance()
    }

    // MARK: - UI Setup

    override func configUI() {
        configView(title: "选择支付方式", bottomTitle: "立即支付")
        self.bottomButton.gradientColors = [UIColor(hexString: "FF5D48")!,UIColor(hexString: "FF3434")!]
        self.bottomButton.gradientDirection = .leftToRight

        // 添加支付视图
        contentView.addSubview(paymentView)
        paymentView.snp.makeConstraints { make in
            make.top.left.bottom.right.equalToSuperview()
        }
    }

    // MARK: - Private Methods

    /// 设置支付视图
    private func setupPaymentView() {
        paymentView.configure(
            totalAmount: totalAmount,
            availableBalance: availableBalance
        )
    }

    /// 初始化支付ViewModel
    private func initializePaymentViewModel() {
        paymentViewModel = CombinedPaymentViewModel()

        // 监听支付流程状态变化
        paymentViewModel?.$paymentFlowState
            .receive(on: DispatchQueue.main)
            .sink { [weak self] state in
                self?.handlePaymentFlowStateChange(state)
            }
            .store(in: &cancellables)

        // 监听可用余额变化
        paymentViewModel?.$availableBalance
            .receive(on: DispatchQueue.main)
            .sink { [weak self] balance in
                self?.handleAvailableBalanceUpdate(balance)
            }
            .store(in: &cancellables)

        // 监听支付验证结果
        paymentViewModel?.$paymentValidationResult
            .receive(on: DispatchQueue.main)
            .sink { [weak self] result in
                if let result = result, !result.isValid {
                    self?.showErrorAlert(result.errorMessage ?? "支付验证失败")
                }
            }
            .store(in: &cancellables)
    }

    /// 获取钱包余额
    private func fetchWalletBalance() {
        paymentViewModel?.fetchWalletBalance()
    }

    /// 处理可用余额更新
    /// - Parameter balance: 可用余额
    private func handleAvailableBalanceUpdate(_ balance: Float) {
        // 更新本地余额
        self.availableBalance = balance

        // 更新支付视图的余额显示
        paymentView.updateAvailableBalance(balance)

        // 更新底部按钮标题
        updateBottomButtonTitle()
    }

    /// 更新底部按钮标题
    private func updateBottomButtonTitle() {
        let buttonTitle = "立即支付 ¥\(String(format: "%.2f", totalAmount))"
        bottomButton.setTitle(buttonTitle, for: .normal)
    }

    // MARK: - Bindings

    override func setupBindings() {
        super.setupBindings()

        // 确认支付按钮
        bottomButton.tapPublisher
            .sink { [weak self] _ in
                self?.confirmPayment()
            }
            .store(in: &cancellables)
    }

    private func confirmPayment() {
        guard let paymentViewModel = paymentViewModel else {
            showErrorAlert("系统错误，请重试")
            return
        }

        let paymentData = paymentView.getPaymentData()

        print("开始支付流程 - 余额: ¥\(paymentData.walletAmount), 第三方: ¥\(paymentData.thirdPartyAmount)")
        print("下单接口参数 - payment_type: \(paymentData.paymentType)")
        print("支付接口参数 - pay_method: \(paymentData.paymentMethod)")

        // 使用ViewModel处理完整的支付流程
        if orderId > 0 {
            // 已有订单，直接支付
            paymentViewModel.processExistingOrderPayment(
                paymentData: paymentData,
                totalAmount: totalAmount,
                orderId: orderId
            )
        } else {
            // 需要创建订单
            guard let orderCreationHandler = orderCreationHandler else {
                showErrorAlert("订单创建处理器未设置")
                return
            }

            paymentViewModel.processCompletePaymentFlow(
                paymentData: paymentData,
                totalAmount: totalAmount,
                orderCreationHandler: orderCreationHandler,
                paymentParametersSetter: paymentParametersSetter ?? { _ in }
            )
        }
    }

    /// 显示错误提示
    override func showErrorAlert(_ message: String) {
        let alert = UIAlertController(title: "提示", message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }

    // MARK: - Public Methods

    /// 设置订单总金额
    func setTotalAmount(_ amount: Float) {
        totalAmount = amount
        paymentView.updateTotalAmount(amount)
    }


    // MARK: - 支付流程状态处理

    private func handlePaymentFlowStateChange(_ state: PaymentFlowState) {
        switch state {
        case .idle:
            hideLoadingIndicator()

        case .creatingOrder:
            showLoadingIndicator(text: "创建订单中...")

        case .orderCreated(let orderId):
            print("订单创建成功，订单ID: \(orderId)")

        case .initiatingPayment:
            showLoadingIndicator(text: "发起支付中...")

        case .waitingThirdPartyPayment:
            showLoadingIndicator(text: "等待支付...")

        case .notifyingPaymentSuccess:
            showLoadingIndicator(text: "处理支付结果中...")

        case .paymentCompleted(let orderId):
            hideLoadingIndicator()
            handlePaymentCompleted(orderId: orderId)

        case .paymentFailed(let error):
            hideLoadingIndicator()
            showErrorAlert(error)
        }
    }

    /// 处理支付完成（包括成功和失败但订单已创建的情况）
    private func handlePaymentCompleted(orderId: Int) {
        dismiss(animated: true) { [weak self] in
            self?.navigateToOrderDetail(orderId: orderId)
        }
    }

    private func navigateToOrderDetail(orderId: Int) {
        guard let currentVC = UIViewController.getCurrentViewController(),
              let navigationController = currentVC.navigationController else { return }

        // 检查是否有支付错误信息，如果有则显示提示
        if let paymentError = paymentViewModel?.paymentError {
            let alert = UIAlertController(title: "提示", message: paymentError, preferredStyle: .alert)
            alert.addAction(UIAlertAction(title: "确定", style: .default) { _ in
                self.performNavigation(navigationController: navigationController, orderId: orderId)
            })
            currentVC.present(alert, animated: true)
        } else {
            performNavigation(navigationController: navigationController, orderId: orderId)
        }
    }

    private func performNavigation(navigationController: UINavigationController, orderId: Int) {
        navigationController.popToRootViewController(animated: true)

        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            let orderDetailController = ProductOrderDetailController()
            orderDetailController.orderId = orderId
            navigationController.pushViewController(orderDetailController, animated: true)
        }
    }

    /// 显示加载指示器
    /// - Parameter text: 加载文本
    private func showLoadingIndicator(text: String = "处理中...") {
        MBProgressHUD.showLoading(in: self.view, text: text)
    }

    /// 隐藏加载指示器
    private func hideLoadingIndicator() {
        MBProgressHUD.hide(for: self.view, animated: true)
    }
}

// MARK: - CombinedPaymentViewDelegate

extension CombinedPaymentController: CombinedPaymentViewDelegate {

    func paymentView(_ view: CombinedPaymentView, didUpdatePaymentAmount amount: Float) {
        let paymentData = view.getPaymentData()
        let totalPaymentAmount = paymentData.walletAmount + paymentData.thirdPartyAmount
        let isValidAmount = abs(totalPaymentAmount - totalAmount) < 0.01 && paymentData.walletAmount <= availableBalance
        print("支付金额变化 - 余额: ¥\(paymentData.walletAmount), 第三方: ¥\(paymentData.thirdPartyAmount), 总计: ¥\(totalPaymentAmount)")
    }
}

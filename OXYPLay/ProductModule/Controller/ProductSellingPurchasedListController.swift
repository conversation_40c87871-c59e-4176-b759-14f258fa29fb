//
//  ProductSellingPurchasedListController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/16.
//

import UIKit
import SnapKit
import Combine
import JXSegmentedView
import MJRefresh

class ProductSellingPurchasedListController: JXBaseViewController {
    
    // MARK: - Properties
    
    /// ViewModel
    private var viewModel: ProductSellingPurchasedViewModel!

    /// 订单事件处理器
    private lazy var orderEventHandler: OrderEventHandler = {
        let handler = OrderEventHandler()
        handler.delegate = self
        return handler
    }()

    /// 订单类型（从父控制器传入）
    var orderType: OrderViewType = .selling

    /// 状态筛选（从父控制器传入）
    var statusFilter: OrderStatusFilter = .all
    
    // MARK: - UI Components
    
    /// 主TableView
    private lazy var tableView: UITableView = {
        let tableView = UITableView(frame: .zero, style: .grouped)
        tableView.backgroundColor = UIColor(hexString: "F3F6F7")
        tableView.separatorStyle = .none
        tableView.delegate = self
        tableView.dataSource = self
        tableView.register(ProductOrderTableCell.self, forCellReuseIdentifier: NSStringFromClass(ProductOrderTableCell.self))
        tableView.register(ProductOrderSellerHeaderView.self, forHeaderFooterViewReuseIdentifier: NSStringFromClass(ProductOrderSellerHeaderView.self))
        tableView.register(ProductOrderSellerFooterView.self, forHeaderFooterViewReuseIdentifier: NSStringFromClass(ProductOrderSellerFooterView.self))
        tableView.estimatedRowHeight = 200
        tableView.rowHeight = UITableView.automaticDimension
        tableView.sectionHeaderHeight = UITableView.automaticDimension
        tableView.sectionFooterHeight = UITableView.automaticDimension
        tableView.estimatedSectionHeaderHeight = 56
        return tableView
    }()
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupViewModel()
        setupUI()
        setupBindings()
        setupRefresh()
        
        // 初始加载数据
        viewModel.refreshData()
    }
    
    // MARK: - Setup Methods
    
    private func setupViewModel() {
        viewModel = ProductSellingPurchasedViewModel(orderType: orderType)
        viewModel.setStatusFilter(statusFilter)
    }
    
    private func setupUI() {
        
        view.addSubview(tableView)
        
        tableView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    override func setupBindings() {
        // 订单列表数据绑定
        viewModel.$orders
            .receive(on: DispatchQueue.main)
            .sink { [weak self] orders in
                self?.updateUI(with: orders)
            }
            .store(in: &cancellables)

        // 绑定刷新状态
        viewModel.bindRefreshState(to: tableView)
            .store(in: &cancellables)
    }

    private func setupRefresh() {
        // 使用BaseViewController的便捷方法设置刷新
        setupRefresh(for: tableView, with: viewModel)
    }
    
    // MARK: - Private Methods

    private func updateUI(with orders: [ProductOrderModel]) {
        tableView.reloadData()

    }
    
    /// 设置状态筛选
    func setStatusFilter(_ filter: OrderStatusFilter) {
        statusFilter = filter
        viewModel.setStatusFilter(filter)
        // 刷新数据
        viewModel.refreshData()
    }
}

// MARK: - UITableViewDataSource

extension ProductSellingPurchasedListController: UITableViewDataSource {
    func numberOfSections(in tableView: UITableView) -> Int {
        return viewModel.orders.count
    }

    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return viewModel.orders[section].items.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let cell = tableView.dequeueReusableCell(withIdentifier: NSStringFromClass(ProductOrderTableCell.self), for: indexPath) as? ProductOrderTableCell else {
            return UITableViewCell()
        }

        let order = viewModel.orders[indexPath.section]
        let item = order.items[indexPath.row]
        let isLast = indexPath.row == order.items.count - 1
        cell.configure(with: order, item: item, isLast: isLast)
        return cell
    }
}

// MARK: - UITableViewDelegate

extension ProductSellingPurchasedListController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)

        let order = viewModel.orders[indexPath.section]
        let orderDetailController = ProductOrderDetailController()
        orderDetailController.orderId = order.order_id
        pushVc(orderDetailController, animated: true)

    }

    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        return UITableView.automaticDimension
    }

    func tableView(_ tableView: UITableView, estimatedHeightForHeaderInSection section: Int) -> CGFloat {
        return 56
    }

    func tableView(_ tableView: UITableView, heightForFooterInSection section: Int) -> CGFloat {
        let order = viewModel.orders[section]
        // 如果没有按钮，footer高度为12，如果有按钮，按钮高度28 + 上下距离12*2 = 52
        return order.action_buttons.isEmpty ? 12 : 52
    }

    func tableView(_ tableView: UITableView, estimatedHeightForFooterInSection section: Int) -> CGFloat {
        return 52
    }

    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        guard let headerView = tableView.dequeueReusableHeaderFooterView(withIdentifier: NSStringFromClass(ProductOrderSellerHeaderView.self)) as? ProductOrderSellerHeaderView else {
            return nil
        }

        let order = viewModel.orders[section]
        headerView.configure(with: order, orderType: orderType)

        return headerView
    }

    func tableView(_ tableView: UITableView, viewForFooterInSection section: Int) -> UIView? {
        guard let footerView = tableView.dequeueReusableHeaderFooterView(withIdentifier: NSStringFromClass(ProductOrderSellerFooterView.self)) as? ProductOrderSellerFooterView else {
            return nil
        }

        let order = viewModel.orders[section]
        footerView.configure(with: order)
        footerView.delegate = self

        return footerView
    }
}

// MARK: - ProductOrderTableCellDelegate

extension ProductSellingPurchasedListController: ProductOrderSellerFooterViewDelegate {
    func footerView(_ footerView: ProductOrderSellerFooterView, didTapButton action: String, order: ProductOrderModel) {
        // 处理按钮点击事件
        handleOrderAction(action, order: order)
    }

    private func handleOrderAction(_ action: String, order: ProductOrderModel) {
        // 使用订单事件处理器统一处理
        orderEventHandler.handleOrderAction(action, order: order)
    }

}

// MARK: - OrderEventHandlerDelegate

extension ProductSellingPurchasedListController: OrderEventHandlerDelegate {

    /// 需要跳转到支付页面
    func orderEventHandler(_ handler: OrderEventHandler, needsPaymentForOrder orderId: Int) {
        // 在列表页面通常不处理支付，可以跳转到订单详情页面
        let orderDetailController = ProductOrderDetailController()
        orderDetailController.orderId = orderId
        pushVc(orderDetailController, animated: true)
    }

    /// 需要跳转到发货页面
    func orderEventHandler(_ handler: OrderEventHandler, needsShippingForOrder orderId: Int) {
        // TODO: 实现发货页面跳转
        print("跳转到发货页面，订单ID: \(orderId)")
    }

    /// 需要跳转到评价页面
    func orderEventHandler(_ handler: OrderEventHandler, needsReviewForOrder orderId: Int, productId: Int, specValueText: String) {
        // TODO: 实现评价页面跳转
        print("跳转到评价页面，订单ID: \(orderId), 商品ID: \(productId)")
    }

    /// 需要跳转到查看评价页面
    func orderEventHandler(_ handler: OrderEventHandler, needsViewReviewForOrder orderId: Int) {
        // TODO: 实现查看评价页面跳转
        print("跳转到查看评价页面，订单ID: \(orderId)")
    }

    /// 需要跳转到物流详情页面
    func orderEventHandler(_ handler: OrderEventHandler, needsViewLogisticsForOrder orderId: Int) {
        // TODO: 实现物流详情页面跳转
        print("跳转到物流详情页面，订单ID: \(orderId)")
    }

    /// 需要跳转到退货发货页面
    func orderEventHandler(_ handler: OrderEventHandler, needsReturnGoodsForOrder orderId: Int) {
        // TODO: 实现退货发货页面跳转
        print("跳转到退货发货页面，订单ID: \(orderId)")
    }

    /// 需要处理申请退款流程
    func orderEventHandler(_ handler: OrderEventHandler, needsApplyRefundForOrder orderId: Int) {
        // 在列表页面通常跳转到订单详情页面处理
        let orderDetailController = ProductOrderDetailController()
        orderDetailController.orderId = orderId
        pushVc(orderDetailController, animated: true)
    }

    /// 需要处理同意退款流程
    func orderEventHandler(_ handler: OrderEventHandler, needsAgreeRefundForOrder orderId: Int) {
        // 在列表页面通常跳转到订单详情页面处理
        let orderDetailController = ProductOrderDetailController()
        orderDetailController.orderId = orderId
        pushVc(orderDetailController, animated: true)
    }

    /// 订单状态已更新，需要刷新页面
    func orderEventHandler(_ handler: OrderEventHandler, orderDidUpdate orderId: Int) {
        // 刷新列表数据
        viewModel.refreshData()
    }

    /// 显示成功消息
    func orderEventHandler(_ handler: OrderEventHandler, showSuccessMessage message: String) {
        // TODO: 显示成功提示
        print("成功: \(message)")
    }

    /// 显示错误消息
    func orderEventHandler(_ handler: OrderEventHandler, showErrorMessage message: String) {
        // TODO: 显示错误提示
        print("错误: \(message)")
    }
}


//
//  SelectAdressController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/24.
//

import UIKit
import SnapKit
import Combine
import Then

class SelectAdressController: BaseViewController {

    // MARK: - Properties
    private var viewModel: LocationSelectViewModel?
    private var addressModel: ProductAddressModel?
    var selectAddressItemModel:ProductAddressItemModel?

    // 用于存储地址编辑过程中的临时数据
    private var tempAddressData: [String: Any] = [:]
    private var isEditingAddress: Bool = false
    private var editingAddressId: String?

    // 地址选择完成的Publisher
    let addressSelectedPublisher = PassthroughSubject<ProductAddressItemModel, Never>()


    // MARK: - UI Components
    private lazy var toolBar = BaseTabToolBar().then {
        $0.delegate = self
    }

    private lazy var tableView = UITableView(frame: .zero, style: .grouped).then {
        $0.backgroundColor = color_F6F8F9
        $0.separatorStyle = .none
        $0.showsVerticalScrollIndicator = false
        $0.delegate = self
        $0.dataSource = self
        $0.register(AddressTableViewCell.self, forCellReuseIdentifier: "AddressTableViewCell")
        $0.sectionHeaderHeight = 44
        $0.sectionFooterHeight = 0.01
        if #available(iOS 15.0, *) {
            $0.sectionHeaderTopPadding = 0
        }
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        setupViewModel()
        configUI()
        setupBindings()
        loadData()
    }

    private func setupViewModel() {
        viewModel = LocationSelectViewModel()
        viewModel?.addressType = .shipping
    }

    private func loadData() {
        viewModel?.fetchShippingAddressList()
    }

    // MARK: - Public Methods


    override func configUI() {
        title = "选择地址"
        view.backgroundColor = color_F6F8F9

        // 配置 toolBar
        toolBar.configureLeftMultipleRightFull(leftItems: [], rightButtonTitle: "确认选择")

        // 添加表格视图
        view.addSubview(tableView)
        // 添加底部工具栏
        view.addSubview(toolBar)
        
        tableView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide)
            make.left.right.equalToSuperview()
            make.bottom.equalTo(toolBar.snp.top)
        }

      
        toolBar.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(ScreenInfo.totalTabBarHeight)
        }
    }

    override func setupBindings() {
        // 监听地址数据变化
        viewModel?.$productAddressModel
            .receive(on: DispatchQueue.main)
            .sink { [weak self] model in
                guard let self = self, let model = model else { return }
                // 更新地址数据并刷新UI
                self.addressModel = model
                //如果有默认项
                if let selectAddressItemModel = self.selectAddressItemModel{
                    self.selectAddress(model: selectAddressItemModel)
                }
                self.tableView.reloadData()
            }
            .store(in: &cancellables)
    }

    // MARK: - Private Methods

    private func addNewAddress() {
        // 清空临时数据
        tempAddressData = [:]
        isEditingAddress = false
        editingAddressId = nil

        showEditLocationController(with: [:], isEditMode: false)
    }

    // MARK: - Action Methods

    private func selectAddress(at index: Int) {
        guard var addressModel = addressModel else { return }

        // 使用模型内部方法选中地址
        ProductAddressItemModel.selectAddress(at: index, in: &addressModel)
        self.addressModel = addressModel

        // 刷新表格
        tableView.reloadData()
    }

    private func selectAddress(model: ProductAddressItemModel) {
        guard var addressModel = addressModel else { return }

        // 使用模型内部方法选中地址
        ProductAddressItemModel.selectAddress(id: model.id, in: &addressModel)
        self.selectAddressItemModel = nil
        self.addressModel = addressModel

        // 刷新表格
        tableView.reloadData()
    }

    private func editAddress(_ address: ProductAddressItemModel) {
        // 设置编辑状态
        isEditingAddress = true
        editingAddressId = address.id

        // 准备地址数据，包含完整的地区信息
        let addressData: [String: Any] = [
            "联系人": address.recipient_name,
            "手机号": address.phone,
            "所在地区": address.region,
            "详细地址": address.detail,
            "isDefualt": address.is_default,  // 保持原有的key
            "is_default": address.is_default, // 添加标准的key
            "address_id": address.id,
            // 地区相关信息
            "province": address.province,
            "province_code": address.province_code,
            "city": address.city,
            "city_code": address.city_code,
            "district": address.district,
            "district_code": address.district_code,
            "street": address.street,
            "street_code": address.street_code
        ]

        showEditLocationController(with: addressData, isEditMode: true)
    }

    // MARK: - Address Edit Flow Methods

    /// 显示地址编辑控制器
    private func showEditLocationController(with data: [String: Any], isEditMode: Bool) {
        let editLocationController = EditLocationController()
        editLocationController.dic = data
        editLocationController.isEditMode = isEditMode
        editLocationController.addressType = 1 // 设置为收货地址类型

        // 监听地址保存事件
        editLocationController.addressSavePublisher
            .receive(on: DispatchQueue.main)
            .sink { [weak self] success in
                if success {
                    // 重新获取地址列表
                    self?.loadData()
                }
            }
            .store(in: &cancellables)

        // 使用导航控制器包装
        let navController = BaseNavigationController(rootViewController: editLocationController)
        customPresent(navController, animated: true)
    }



    /// 刷新地址列表数据
    private func refreshAddressList() {
        // 重新获取最新的地址数据
        viewModel?.fetchShippingAddressList()
    }
}



// MARK: - UITableViewDataSource

extension SelectAdressController: UITableViewDataSource {
    func numberOfSections(in tableView: UITableView) -> Int {
        return 2 // 原地址 + 新地址
    }

    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        guard let addressModel = addressModel else { return 0 }

        if section == 0 {
            // 原地址部分 - 显示is_default为true的地址
            return addressModel.address_list.filter { $0.is_default }.count
        } else {
            // 新地址部分 - 显示is_default为false的地址
            return addressModel.address_list.filter { !$0.is_default }.count
        }
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "AddressTableViewCell", for: indexPath) as! AddressTableViewCell

        guard let addressModel = addressModel else { return cell }

        let address: ProductAddressItemModel
        if indexPath.section == 0 {
            // 原地址 - 筛选is_default为true的地址
            let defaultAddresses = addressModel.address_list.filter { $0.is_default }
            address = defaultAddresses[indexPath.row]
            cell.configure(with: address, isDefaultSection: true)
        } else {
            // 新地址 - 筛选is_default为false的地址
            let nonDefaultAddresses = addressModel.address_list.filter { !$0.is_default }
            address = nonDefaultAddresses[indexPath.row]
            cell.configure(with: address, isDefaultSection: false)
        }

        cell.editButtonTapped = { [weak self] address in
            self?.editAddress(address)
        }

        cell.selectionButtonTapped = { [weak self] address in
            // 选择地址列表中的地址
            if let index = addressModel.address_list.firstIndex(where: { $0.id == address.id }) {
                self?.selectAddress(at: index)
            }
        }

        return cell
    }
}

// MARK: - UITableViewDelegate

extension SelectAdressController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        let headerView = UIView()
        headerView.backgroundColor = color_F6F8F9

        let titleLabel = UILabel().then {
            $0.font = UIFont.systemFont(ofSize: 16, weight: .medium)
            $0.textColor = color_3D3E40
        }

        if section == 0 {
            titleLabel.text = "原地址"
        } else {
            titleLabel.text = "新地址"

            // 添加新增地址按钮
            let addButton = UIButton(type: .custom).then {
                $0.setTitle("+ 新增地址", for: .normal)
                $0.setTitleColor(color_blue, for: .normal)
                $0.titleLabel?.font = UIFont.systemFont(ofSize: 12, weight: .medium)
            }

            addButton.tapPublisher
                .sink { [weak self] _ in
                    self?.addNewAddress()
                }
                .store(in: &cancellables)

            headerView.addSubview(addButton)
            addButton.snp.makeConstraints { make in
                make.right.equalToSuperview().offset(-16)
                make.centerY.equalToSuperview()
            }
        }

        headerView.addSubview(titleLabel)
        titleLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.centerY.equalToSuperview()
        }

        return headerView
    }
}

// MARK: - BaseTabToolBarDelegate

extension SelectAdressController: TabToolBarDelegate {
    func tabToolBar(_ toolBar: BaseTabToolBar, didClickRightButtonAt index: Int, item: ToolBarButtonItem?) {
        // 确认选择地址
        guard let addressModel = addressModel else { return }

        // 使用模型的selectedAddress方法获取选中的地址
        if let selectedAddress = addressModel.selectedAddress() {
            print("选中的地址: \(selectedAddress.recipient_name)")
            // 发送选中的地址给上级页面
            addressSelectedPublisher.send(selectedAddress)
            navigationController?.popViewController(animated: true)
        } else {
            // 显示提示信息
            showErrorAlert("请选择一个地址")
        }
    }
}

// MARK: - AddressTableViewCell

class AddressTableViewCell: UITableViewCell {

    // MARK: - Properties
    var editButtonTapped: ((ProductAddressItemModel) -> Void)?
    var selectionButtonTapped: ((ProductAddressItemModel) -> Void)?
    private var address: ProductAddressItemModel?

    // MARK: - UI Components
    private lazy var containerView = UIView().then {
        $0.backgroundColor = .white
        $0.layer.cornerRadius = 16
        $0.layer.masksToBounds = true
    }

    private lazy var defaultLabel = UILabel().then {
        $0.text = "默认"
        $0.font = UIFont.systemFont(ofSize: 12)
        $0.backgroundColor = UIColor(hexString: "#FF0000",transparency: 0.08)
        $0.textColor = UIColor(hexString: "#FF0000")
        $0.textAlignment = .center
        $0.layer.cornerRadius = 16
        $0.layer.masksToBounds = true
        $0.layer.maskedCorners = [.layerMinXMaxYCorner,.layerMaxXMinYCorner]
        $0.isHidden = true
    }

    private lazy var radioButton = UIButton(type: .custom).then {
        $0.setImage(UIImage(named: "baselist_single_normal"), for: .normal)
        $0.setImage(UIImage(named: "baselist_single_select"), for: .selected)
    }

    private lazy var namePhoneLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        $0.textColor = color_2B2C2F
    }

    private lazy var editButton = UIButton(type: .custom).then {
        $0.setImage(UIImage(systemName: "pencil"), for: .normal)
        $0.tintColor = color_999DA1
    }

    private lazy var addressLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 12)
        $0.textColor = UIColor(hexString: "#2B2C2F", transparency: 0.56)
        $0.numberOfLines = 0
    }

    private lazy var detailLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 12)
        $0.textColor = UIColor(hexString: "#2B2C2F", transparency: 1)
        $0.numberOfLines = 0
    }

    // MARK: - Initialization
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
        setupBindings()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - Setup
    private func setupUI() {
        backgroundColor = color_F6F8F9
        selectionStyle = .none

        contentView.addSubview(containerView)
        containerView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(0)
            make.left.equalToSuperview().offset(12)
            make.right.equalToSuperview().offset(-12)
            make.bottom.equalToSuperview().offset(-12)
        }

        containerView.addSubview(defaultLabel)
        containerView.addSubview(radioButton)
        containerView.addSubview(namePhoneLabel)
        containerView.addSubview(editButton)
        containerView.addSubview(addressLabel)
        containerView.addSubview(detailLabel)

        setupConstraints()
    }

    private func setupConstraints() {
        defaultLabel.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.right.equalToSuperview()
            make.width.equalTo(42)
            make.height.equalTo(19)
        }

        radioButton.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.left.equalToSuperview().offset(12)
            make.width.height.equalTo(20)
        }

        namePhoneLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(12)
            make.left.equalTo(36)
            make.right.lessThanOrEqualTo(editButton.snp.left).offset(-8)
        }

        editButton.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.right.equalTo(-12)
            make.width.height.equalTo(20)
        }

        addressLabel.snp.makeConstraints { make in
            make.top.equalTo(namePhoneLabel.snp.bottom).offset(10)
            make.left.equalTo(namePhoneLabel)
        }

        detailLabel.snp.makeConstraints { make in
            make.top.equalTo(addressLabel.snp.bottom).offset(10)
            make.left.equalTo(namePhoneLabel)
            make.right.equalTo(editButton.snp.left).offset(-8)
            make.bottom.equalTo(-12)
        }
    }

    private func setupBindings() {
        editButton.addTarget(self, action: #selector(editButtonTapped(_:)), for: .touchUpInside)
        radioButton.addTarget(self, action: #selector(radioButtonTapped(_:)), for: .touchUpInside)

        // 添加整个容器的点击手势
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(containerTapped))
        containerView.addGestureRecognizer(tapGesture)
        containerView.isUserInteractionEnabled = true
    }

    // MARK: - Actions
    @objc private func editButtonTapped(_ sender: UIButton) {
        guard let address = address else { return }
        editButtonTapped?(address)
    }

    @objc private func radioButtonTapped(_ sender: UIButton) {
        guard let address = address else { return }
        selectionButtonTapped?(address)
    }

    @objc private func containerTapped() {
        guard let address = address else { return }
        selectionButtonTapped?(address)
    }

    // MARK: - Configuration
    func configure(with address: ProductAddressItemModel, isDefaultSection: Bool = false) {
        self.address = address

        radioButton.isSelected = address.isSelect
        namePhoneLabel.text = "\(address.recipient_name) 86-\(address.phone.maskPhoneNumber)"
        addressLabel.text = address.region
        detailLabel.text = address.detail

        // 根据是否为默认地址区域来显示默认标签
        if isDefaultSection {
            // 原地址区域，始终显示默认标签
            defaultLabel.isHidden = false
        } else {
            // 新地址区域，根据is_default字段显示（虽然在新地址区域is_default应该都是false）
            defaultLabel.isHidden = !address.is_default
        }
    }

    /// 隐藏编辑按钮
    func hideEditButton() {
        editButton.isHidden = true
    }
}

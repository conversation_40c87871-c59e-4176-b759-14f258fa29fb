//
//  SelectRefundAddressController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/8/4.
//

import UIKit
import SnapKit
import Combine
import Then

/// 选择退货地址控制器
class SelectRefundAddressController: BasePresentController {

    // MARK: - Properties
    private var viewModel = LocationSelectViewModel()
    private var addressModel: ProductAddressModel?
    private var selectedAddress: ProductAddressItemModel?

    // 地址选择完成的Publisher
    let addressSelectedPublisher = PassthroughSubject<ProductAddressItemModel, Never>()

    // MARK: - UI Components
    private lazy var tableView = UITableView(frame: .zero, style: .grouped).then {
        $0.backgroundColor = color_F6F8F9
        $0.separatorStyle = .none
        $0.showsVerticalScrollIndicator = false
        $0.delegate = self
        $0.dataSource = self
        $0.register(AddressTableViewCell.self, forCellReuseIdentifier: "AddressTableViewCell")
        $0.sectionHeaderHeight = 44
        $0.sectionFooterHeight = 0.01
        if #available(iOS 15.0, *) {
            $0.sectionHeaderTopPadding = 0
        }
    }

    // MARK: - Properties

    override var presentationHeight: CGFloat? {
        return nil
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        configUI()
        loadData()
    }

    // MARK: - UI Setup

    override func configUI() {
        configView(title: "选择退货地址", bottomTitle: "发送地址")
        viewModel.addressType = .refund
        // 添加表格视图
        contentView.addSubview(tableView)
        tableView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.height.equalTo(44*4+12+16)
        }
    }

    // MARK: - Bindings

    override func setupBindings() {
        super.setupBindings()

        // 底部按钮点击事件
        bottomButton.tapPublisher
            .sink { [weak self] _ in
                guard let self = self else { return }
                self.confirmSelection()
            }
            .store(in: &cancellables)
      
        // 监听地址数据变化
        viewModel.$productAddressModel
            .receive(on: DispatchQueue.main)
            .sink { [weak self] model in
                guard let self = self else { return }
                print("SelectRefundAddressController: 收到地址数据更新, model: \(model?.address_list.count ?? 0) 条地址")
                self.addressModel = model
                self.tableView.reloadData()
            }
            .store(in: &cancellables)
    }

   
    private func loadData() {
        // 获取退货地址数据（type=2）
        print("SelectRefundAddressController: 开始加载退货地址数据")
        viewModel.fetchRefundAddressList()
    }

    private func confirmSelection() {
        guard let selectedAddress = selectedAddress else {
            showErrorAlert("请选择一个退货地址")
            return
        }
        
        // 发送选中的地址
        addressSelectedPublisher.send(selectedAddress)
        dismiss(animated: true)
    }

    private func selectAddress(_ address: ProductAddressItemModel) {
        selectedAddress = address

        // 更新UI选中状态
        if var addressModel = addressModel {
            // 清除所有选中状态
            for i in 0..<addressModel.address_list.count {
                addressModel.address_list[i].isSelect = false
            }

            // 设置当前选中
            if let index = addressModel.address_list.firstIndex(where: { $0.id == address.id }) {
                addressModel.address_list[index].isSelect = true
            }

            self.addressModel = addressModel
            tableView.reloadData()
        }
    }

    private func addNewRefundAddress() {
        let editLocationController = EditLocationController()
        editLocationController.dic = [:]
        editLocationController.isEditMode = false
        editLocationController.addressType = 2 // 设置为退货地址类型

        // 监听地址保存事件
        editLocationController.addressSavePublisher
            .receive(on: DispatchQueue.main)
            .sink { [weak self] success in
                if success {
                    // 重新获取退货地址列表
                    self?.loadData()
                }
            }
            .store(in: &cancellables)

        // 使用导航控制器包装
        let navController = BaseNavigationController(rootViewController: editLocationController)
        customPresent(navController, animated: true)
    }
  

    private func editRefundAddress(_ address: ProductAddressItemModel) {
        let editLocationController = EditLocationController()

        // 设置编辑模式和地址数据
        editLocationController.isEditMode = true
        editLocationController.addressType = 2 // 设置为退货地址类型
        editLocationController.dic = [
            "联系人": address.recipient_name,
            "手机号": address.phone,
            "所在地区": address.region,
            "详细地址": address.detail,
            "isDefualt": address.is_default,  // 保持原有的key
            "is_default": address.is_default, // 添加标准的key
            "address_id": address.id,
            // 地区相关信息
            "province": address.province,
            "province_code": address.province_code,
            "city": address.city,
            "city_code": address.city_code,
            "district": address.district,
            "district_code": address.district_code,
            "street": address.street,
            "street_code": address.street_code
        ]

        // 监听地址保存事件
        editLocationController.addressSavePublisher
            .receive(on: DispatchQueue.main)
            .sink { [weak self] success in
                if success {
                    // 重新获取退货地址列表
                    self?.loadData()
                }
            }
            .store(in: &cancellables)

        // 使用导航控制器包装
        let navController = BaseNavigationController(rootViewController: editLocationController)
        customPresent(navController, animated: true)
    }


}

// MARK: - UITableViewDataSource

extension SelectRefundAddressController: UITableViewDataSource {
    func numberOfSections(in tableView: UITableView) -> Int {
        return 1 // 只显示地址列表
    }

    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        guard let addressModel = addressModel else { return 0 }
        return addressModel.address_list.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "AddressTableViewCell", for: indexPath) as! AddressTableViewCell

        guard let addressModel = addressModel else { return cell }

        // 地址列表
        let address = addressModel.address_list[indexPath.row]
        cell.configure(with: address, isDefaultSection: address.is_default)

        // 设置编辑按钮回调
        cell.editButtonTapped = { [weak self] address in
            self?.editRefundAddress(address)
        }

        cell.selectionButtonTapped = { [weak self] address in
            self?.selectAddress(address)
        }

        return cell
    }
}

// MARK: - UITableViewDelegate

extension SelectRefundAddressController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        let headerView = UIView()
        headerView.backgroundColor = color_F6F8F9

        let titleLabel = UILabel().then {
            $0.font = UIFont.systemFont(ofSize: 16, weight: .medium)
            $0.textColor = color_3D3E40
            $0.text = "选择退货地址"
        }

        // 添加新增地址按钮
        let addButton = UIButton(type: .custom).then {
            $0.setTitle("+ 新增退货地址", for: .normal)
            $0.setTitleColor(color_blue, for: .normal)
            $0.titleLabel?.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        }

        addButton.tapPublisher
            .sink { [weak self] _ in
                self?.addNewRefundAddress()
            }
            .store(in: &cancellables)

        headerView.addSubview(addButton)
        addButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
        }

        headerView.addSubview(titleLabel)
        titleLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.centerY.equalToSuperview()
        }

        return headerView
    }
}

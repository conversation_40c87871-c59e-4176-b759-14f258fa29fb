//
//  ProductSelectController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/14.
//

import UIKit
class ProductSelectController: BasePresentController {
    // MARK: - 属性

    @Published var detailModel: ProductDetailModel?
    /// 选择结果
    let selectCompletePublisher = PassthroughSubject<Bool, Never>()
    // MARK: - UI组件
    
    private lazy var headerView = ProductSelectHeaderView().then {
        $0.delegate = self
        $0.backgroundColor = .white
        $0.layer.cornerRadius = 16
        $0.masksToBounds = true
    }
    lazy var specOptionView = ProductSpecOptionView().then {
        $0.delegate = self
    }
    
    // MARK: - 生命周期
    
    // 实现CustomPresentable协议
    override var presentationHeight: CGFloat? {
        return kScreenHeight - 192
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        configUI()
    }


    /// 统一更新所有视图
    private func updateAllViews(with model: ProductDetailModel) {
        specOptionView.updateModel(model)

        // 更新头部视图
        headerView.configure(imageURL: model.getImage(), price: model.getcurrentQuantityPrice().formattedPrice, shipping: model.product.postage_type_text)
        headerView.updateQuantity(model.currentQuantity)

        // 更新头部视图的库存限制
        let currentStock = model.getCurrentStock()
        headerView.updateStockLimit(currentStock)
    }
    // 设置单向数据绑定
   override func setupBindings() {
       super.setupBindings()
        $detailModel
            .compactMap { $0 }
            .sink { [weak self] model in
                guard let self = self else { return }
                // 更新所有子视图
                self.updateAllViews(with: model)
            }
            .store(in: &cancellables)
        // 配置确认按钮事件
        bottomButton.tapPublisher
            .sink { [weak self] _ in
                self?.confirmSelection()
            }
            .store(in: &cancellables)
    }
    
    override func configUI() {
        configView(title: "选择规格", bottomTitle: "确定")
        self.bottomButton.gradientColors = [UIColor(hexString: "#FF5D48")!, UIColor(hexString: "#FF3434")!]
        // 添加自定义views
        contentView.addSubview(headerView)
        contentView.addSubview(specOptionView)
        
        // 设置约束
        headerView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(12)
            make.left.equalToSuperview().offset(12)
            make.right.equalToSuperview().offset(-12)
            make.height.equalTo(80)
        }
        specOptionView.snp.makeConstraints { make in
            make.top.equalTo(headerView.snp.bottom).offset(12)
            make.left.equalToSuperview().offset(12)
            make.right.equalToSuperview().offset(-12)
            make.bottom.equalTo(-12)
        }
       
    }
   
    // MARK: - 事件处理

    private func confirmSelection() {
        dismiss(animated: true)
        selectCompletePublisher.send(true)
    }


}

// MARK: - ProductHeaderViewDelegate

// MARK: - ProductSelectHeaderViewDelegate
extension ProductSelectController: ProductSelectHeaderViewDelegate {
    func quantityDidChange(_ quantity: Int) {
        guard var model = detailModel else { return }

        // 检查库存限制
        let currentStock = model.getCurrentStock()
        let validQuantity = min(quantity, currentStock)

        // 如果请求的数量超过库存，显示提示
        if quantity > currentStock {
            showStockLimitAlert(stock: currentStock)
        }

        model.currentQuantity = validQuantity
        detailModel = model
    }

    /// 显示库存不足提示
    private func showStockLimitAlert(stock: Int) {
        let message = stock > 0 ? "库存不足，最多只能选择\(stock)件" : "该商品暂无库存"

        let alert = UIAlertController(title: "提示", message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
}

// MARK: - ProductSpecOptionViewDelegate
extension ProductSelectController: ProductSpecOptionViewDelegate {
    func specOptionDidChange(_ model: ProductDetailModel) {
        detailModel = model
    }
}


//
//  ProductOrderConfirmation​Controller.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/15.
//

class ProductOrderConfirmation​Controller: BaseViewController {
    // MARK: - 属性

    var detailModel: HomeDetailModel?
    var productDetailModel: ProductDetailModel?
    var dic = Dictionary<String, Any>()
    var viewModel: ProductOrderConfirmation​ViewModel?
    var locationViewModel: LocationSelectViewModel?
    private lazy var toolBar = BaseTabToolBar().then {
        $0.delegate = self
    }
    lazy var listView = BaseListView().then{
        $0.delegate = self
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        configUI()
        configureOrderListItems()
        setupBindings()
        getData()
    }

    /// 配置UI界面
    override func configUI() {
        title = "确认订单"

        // 配置 toolBar - 初始化时显示基础文本，后续会根据金额更新
        toolBar.configureLeftMultipleRightFull(leftItems: [], rightButtonTitle: "立即支付")
        toolBar.setRightButtonGradient(colors: [UIColor(hexString: "FF5D48")!,UIColor(hexString: "FF3434")!], direction: .leftToRight)

        view.addSubview(toolBar)
        toolBar.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(ScreenInfo.totalTabBarHeight)
        }
        view.addSubview(listView)
        listView.snp.makeConstraints { make in
            make.top.equalTo(self.view.safeAreaLayoutGuide.snp.top).offset(12)
            make.leading.trailing.equalToSuperview()
            make.bottom.equalTo(toolBar.snp.top).offset(0)
        }
    }

    /// 获取数据
    func getData() {
        locationViewModel?.fetchShippingAddressList()
        if let detailModel = detailModel {
            viewModel?.fetchCouponList(advertiser_id: detailModel.user_id, order_amount: detailModel.price)
        }
        if let proDuctDetailModel = productDetailModel {
            viewModel?.fetchCouponList(advertiser_id: proDuctDetailModel.product.seller_id, order_amount: proDuctDetailModel.getcurrentQuantityPrice())
        }
    }

    /// 设置数据绑定
    override func setupBindings() {
        self.viewModel = ProductOrderConfirmation​ViewModel()
        self.locationViewModel = LocationSelectViewModel()
        locationViewModel?.addressType = .shipping

        // 监听优惠券数据变化
        viewModel?.$couponModel
            .receive(on: DispatchQueue.main)
            .sink { [weak self] model in
                guard let model = model else {return }
                self?.productDetailModel?.coupon = model
                self?.detailModel?.coupon = model
                self?.configureOrderListItems()
            }
            .store(in: &cancellables)
        // 监听地址数据变化
        locationViewModel?.$productAddressModel
            .receive(on: DispatchQueue.main)
            .sink { [weak self] model in
                guard let model = model else { return }
                self?.viewModel?.selectAddressModel = model.selectedAddress()
                self?.refreshUI()
            }
            .store(in: &cancellables)

        // 监听优惠券变化，用于更新底部按钮显示
        viewModel?.$couponModel
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                guard let self = self else { return }
                // 优惠券变化时更新底部按钮显示金额
                self.updateBottomButtonTitle()
            }
            .store(in: &cancellables)
        
    }
}

// MARK: - List Configuration
extension ProductOrderConfirmation​Controller {

    func configureOrderListItems() {
        // 创建地址项配置
        let locationItem = createLocationItem()
        if let detailModel = detailModel {
            let items: [[ListItemConfig]] = [
                [
                    ListItemConfig(type: .orderConfirmation, identifier: "orderConfirmation", data: detailModel)
                ],
                [
                    locationItem
                ]
            ]
            // 设置列表项
            listView.setItems(items)
        }
        if let productDetailModel = productDetailModel {
            let items: [[ListItemConfig]] = [
                [
                    ListItemConfig(type: .orderConfirmation, identifier: "orderConfirmation", data: productDetailModel)
                ],
                [
                    locationItem
                ]
            ]
            // 设置列表项
            listView.setItems(items)
        }
    }


    /// 更新底部按钮标题
    /// 显示"立即支付￥XX"，金额为到手价（原价-优惠券）
    private func updateBottomButtonTitle() {
        let totalAmount = calculateTotalAmount()
        let buttonTitle = "立即支付￥\(String(format: "%.2f", totalAmount))"
        toolBar.updateRightButtonTitle(buttonTitle)
    }

    /// 刷新UI
    private func refreshUI() {
        configureOrderListItems()
        updateBottomButtonTitle()
    }
}
// MARK: - Address Management
extension ProductOrderConfirmation​Controller {

    /// 创建地址项配置
    func createLocationItem() -> ListItemConfig {
        // 检查是否有选中的地址
        if let selectedAddress = viewModel?.selectAddressModel {
            // 有选中的地址，显示地址信息
            return ListItemConfig(type: .orderLocation, identifier: "orderLocation", data: selectedAddress)
        } else {
            // 没有选中的地址，显示"请填写收货地址"
            return ListItemConfig(type: .select, identifier: "baselist_location", placeholder: "请填写收货地址", iconString: "baselist_location")
        }
    }
}
// MARK: - Payment Management
extension ProductOrderConfirmation​Controller {

    /// 显示支付方式选择弹窗
    /// 点击立即支付按钮时调用，弹出CombinedPaymentController进行支付方式选择
    func showPaymentMethodSelection() {
        let combinedPaymentController = CombinedPaymentController()

        // 设置订单总金额（到手价）
        let totalAmount = calculateTotalAmount()
        combinedPaymentController.setTotalAmount(totalAmount)

        // 设置订单创建处理器
        combinedPaymentController.orderCreationHandler = { [weak self] in
            return self?.createOrderPublisher() ?? Fail(error: NSError(domain: "OrderCreation", code: -1, userInfo: [NSLocalizedDescriptionKey: "无法创建订单"])).eraseToAnyPublisher()
        }

        // 设置支付参数设置器
        combinedPaymentController.paymentParametersSetter = { [weak self] paymentData in
            self?.setPaymentParametersToViewModel(paymentData)
        }

        customPresent(UINavigationController(rootViewController: combinedPaymentController), animated: true)
    }

    /// 创建订单的Publisher
    /// - Returns: 返回订单ID的Publisher
    private func createOrderPublisher() -> AnyPublisher<Int, Error> {
        guard let viewModel = viewModel else {
            return Fail(error: NSError(domain: "OrderCreation", code: -1, userInfo: [NSLocalizedDescriptionKey: "系统错误，请重试"]))
                .eraseToAnyPublisher()
        }

        // 验证地址选择
        guard let selectAddressModel = viewModel.selectAddressModel else {
            return Fail(error: NSError(domain: "OrderCreation", code: -1, userInfo: [NSLocalizedDescriptionKey: "请选择收货地址"]))
                .eraseToAnyPublisher()
        }

        // 创建订单
        if let serviceModel = detailModel {
            return createServiceOrderPublisher(serviceModel: serviceModel)
        } else if let productModel = productDetailModel {
            return createProductOrderPublisher(productModel: productModel)
        } else {
            return Fail(error: NSError(domain: "OrderCreation", code: -1, userInfo: [NSLocalizedDescriptionKey: "订单信息错误"]))
                .eraseToAnyPublisher()
        }
    }

    /// 设置支付参数到ViewModel
    /// - Parameter paymentData: 支付数据
    private func setPaymentParametersToViewModel(_ paymentData: PaymentData) {
        guard let viewModel = viewModel else { return }

        // 设置支付金额
        viewModel.updatePaymentAmounts(walletAmount: paymentData.walletAmount, thirdPartyAmount: paymentData.thirdPartyAmount)

        // 设置下单接口的payment_type参数
        viewModel.updateCombineThirdPartyPaymentMethod(paymentData.paymentType)

        print("设置下单参数 - payment_type: \(paymentData.paymentType), 余额: ¥\(paymentData.walletAmount), 第三方: ¥\(paymentData.thirdPartyAmount)")
    }

    /// 创建服务订单Publisher
    private func createServiceOrderPublisher(serviceModel: HomeDetailModel) -> AnyPublisher<Int, Error> {
        return Future<Int, Error> { [weak self] promise in
            self?.viewModel?.createServiceOrder(serviceModel: serviceModel) { [weak self] success, message in
                if success {
                    if let orderId = self?.getOrderIdFromCreateResult() {
                        promise(.success(orderId))
                    } else {
                        promise(.failure(NSError(domain: "OrderCreation", code: -1, userInfo: [NSLocalizedDescriptionKey: "订单创建失败：无效的订单ID"])))
                    }
                } else {
                    promise(.failure(NSError(domain: "OrderCreation", code: -1, userInfo: [NSLocalizedDescriptionKey: message])))
                }
            }
        }
        .eraseToAnyPublisher()
    }

    /// 创建商品订单Publisher
    private func createProductOrderPublisher(productModel: ProductDetailModel) -> AnyPublisher<Int, Error> {
        return Future<Int, Error> { [weak self] promise in
            self?.viewModel?.buyNowProduct(productModel: productModel) { [weak self] success, message in
                if success {
                    if let orderId = self?.getOrderIdFromCreateResult() {
                        promise(.success(orderId))
                    } else {
                        promise(.failure(NSError(domain: "OrderCreation", code: -1, userInfo: [NSLocalizedDescriptionKey: "订单创建失败：无效的订单ID"])))
                    }
                } else {
                    promise(.failure(NSError(domain: "OrderCreation", code: -1, userInfo: [NSLocalizedDescriptionKey: message])))
                }
            }
        }
        .eraseToAnyPublisher()
    }

    /// 从创建订单结果中获取订单ID
    private func getOrderIdFromCreateResult() -> Int? {
        guard let result = viewModel?.createOrderResult else { return nil }

        if !result.order_ids.isEmpty {
            return result.order_ids.first
        } else if result.order_id > 0 {
            return result.order_id
        }

        return nil
    }

    /// 计算订单总金额
    func calculateTotalAmount() -> Float {
        if let serviceModel = detailModel {
            let servicePrice = Float(serviceModel.price) ?? 0.0
            let discountAmount = Float(viewModel?.couponModel?.getCurrentSelectedPrice() ?? "0") ?? 0.0
            return servicePrice - discountAmount
        } else if let productModel = productDetailModel {
            let productPrice = Float(productModel.getcurrentQuantityPrice()) ?? 0.0
            let discountAmount = Float(viewModel?.couponModel?.getCurrentSelectedPrice() ?? "0") ?? 0.0
            return productPrice - discountAmount
        }
        return 0.0
    }


}

// MARK: - Coupon Management
extension ProductOrderConfirmation​Controller {

    /// 处理优惠券选择完成
    func handleCouponSelectionCompleted() {
        // 优惠券选择完成，可以从viewModel的couponModel中获取当前选中的优惠券
        if let selectedCoupon = viewModel?.couponModel?.getCurrentSelectedCoupon() {
            print("选中的优惠券: \(selectedCoupon.title)")
            print("优惠券金额: ¥\(selectedCoupon.amount)")
        } else {
            print("没有选中任何优惠券")
        }

        // 更新UI显示选中的优惠券信息
        configureOrderListItems()
    }
}



extension ProductOrderConfirmation​Controller: TabToolBarDelegate {
    func tabToolBar(_ toolBar: BaseTabToolBar, didClickRightButtonAt index: Int, item: ToolBarButtonItem?) {
        // 处理立即支付按钮点击
        handlePaymentButtonClick()
    }

    /// 处理支付按钮点击
    /// 确认订单页面的立即支付按钮点击，验证信息后弹出支付方式选择页面
    private func handlePaymentButtonClick() {
        // 验证必要信息
        guard let viewModel = viewModel else {
            showErrorAlert(message: "系统错误，请重试")
            return
        }

        // 验证地址选择
        guard let selectAddressModel = viewModel.selectAddressModel else {
            showErrorAlert(message: "请选择收货地址")
            return
        }

        // 弹出支付方式选择页面
        showPaymentMethodSelection()
    }

    /// 显示错误提示
    private func showErrorAlert(message: String) {
        let alert = UIAlertController(title: "提示", message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }


}

extension ProductOrderConfirmation​Controller:BaseListViewDelegate{

    func listViewUpdate(_ listView: BaseListView, with data: Any?) {
        // 确认订单页面不再处理支付方式选择更新
        // 支付方式选择已移至CombinedPaymentController中处理
    }

    func listViewClick(_ listView: BaseListView, config: ListItemConfig) {
       
        //跳转地址列表
        if config.identifier == "orderLocation" || config.identifier == "baselist_location"{
            let vc = SelectAdressController()
            vc.selectAddressItemModel = viewModel?.selectAddressModel
            // 监听地址选择事件
            vc.addressSelectedPublisher
                .receive(on: DispatchQueue.main)
                .sink { [weak self] selectedAddress in
                    self?.viewModel?.selectAddressModel = selectedAddress
                    self?.refreshUI()
                }
                .store(in: &cancellables)

            pushVc(vc, animated: true)
        }
        //选择优惠卷
        if config.identifier == "orderConfirmation"{
            let vc = CouponSelectController()
            // 传递优惠券数据
            if let couponModel = viewModel?.couponModel {
                vc.setCouponModel(couponModel)
            }

            // 监听优惠券选择结果
            vc.couponSelectedPublisher
                .receive(on: RunLoop.main)
                .sink { [weak self] _ in
                    guard let self = self else { return }
                    // 获取更新后的优惠券模型并更新到viewModel
                    if let updatedCouponModel = vc.getUpdatedCouponModel() {
                        self.viewModel?.couponModel = updatedCouponModel
                        // 同时更新productDetailModel中的coupon数据
                        self.productDetailModel?.coupon = updatedCouponModel
                    }
                    // 优惠券选择完成，更新UI
                    self.handleCouponSelectionCompleted()
                }
                .store(in: &cancellables)

            customPresent(vc, animated: true)
        }
    }
}

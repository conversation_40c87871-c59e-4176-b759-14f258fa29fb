import SmartCodable

/// 商品详情主模型
struct ProductDetailModel: SmartCodable {
    var product: ProductDetailProduct = ProductDetailProduct()
    var variants: ProductDetailVariantData = ProductDetailVariantData()
    var currentQuantity:Int = 1
    var coupon:CouponModel = CouponModel()

    /// 设置所有商品变体的默认选项
    /// - Returns: 设置了默认选项的ProductDetailModel
    mutating func setDefaultSelections() -> ProductDetailModel {
        // 遍历所有商品变体，为每个变体设置默认选项
        for variantIndex in 0..<variants.data.count {
            // 第一个变体默认选中，其他变体默认不选中
            variants.data[variantIndex].isSelect = (variantIndex == 0)

            // 为每个变体的所有规格设置默认选项
            for specIndex in 0..<variants.data[variantIndex].specs.count {
                // 查找并设置第一个可选的选项为选中状态
                if let firstSelectableIndex = variants.data[variantIndex].specs[specIndex].values.firstIndex(where: { $0.selectable }) {
                    // 先将该规格的所有选项设为未选中
                    for valueIndex in 0..<variants.data[variantIndex].specs[specIndex].values.count {
                        variants.data[variantIndex].specs[specIndex].values[valueIndex].isSelect = false
                    }
                    // 设置找到的第一个可选项为选中状态
                    variants.data[variantIndex].specs[specIndex].values[firstSelectableIndex].isSelect = true
                }
            }
        }
        return self
    }

    /// 选择指定索引的商品变体
    /// - Parameter index: 要选择的变体索引
    /// - Returns: 更新后的ProductDetailModel
    mutating func selectVariant(at index: Int) -> ProductDetailModel {
        // 检查索引是否有效
        guard index >= 0 && index < variants.data.count else {
            return self
        }

        // 更新所有变体的选中状态
        for i in 0..<variants.data.count {
            variants.data[i].isSelect = (i == index)
        }

        return self
    }
    ///获取当前价格
    func getcurrentQuantityPrice()->String{
        if let selectedModel = self.variants.data.filter({ $0.isSelect }).first {
            var array = [String]()
            selectedModel.specs.forEach { spec in
                spec.values.forEach { vlu in
                    if vlu.isSelect == true {
                        array.append(vlu.id)
                    }
                }
            }
            if let sku =  selectedModel.skus.filter({$0.spec_value_ids == array}).first{
                return  "\((Float(sku.price) ?? 0) * Float( currentQuantity))"
            }
        }
        return ""
    }
    ///获取当前原价
    func getOrigin_price()->String{
        if let selectedModel = self.variants.data.filter({ $0.isSelect }).first {
            var array = [String]()
            selectedModel.specs.forEach { spec in
                spec.values.forEach { vlu in
                    if vlu.isSelect == true {
                        array.append(vlu.id)
                    }
                }
            }
            if let sku =  selectedModel.skus.filter({$0.spec_value_ids == array}).first{
                return sku.origin_price.formattedPrice
            }
        }
        return ""
    }
    ///获取当前商品图
    func getImage()->String{
        if let selectedModel = self.variants.data.filter({ $0.isSelect }).first {
            return selectedModel.cover_image
        }
        return ""
    }

    /// 获取当前选中SKU的库存数量
    /// - Returns: 当前选中规格的库存数量，如果没有选中或找不到对应SKU则返回0
    func getCurrentStock() -> Int {
        guard let selectedModel = self.variants.data.filter({ $0.isSelect }).first else {
            return 0
        }

        // 获取当前选中的规格值ID数组
        var selectedSpecValueIds = [String]()
        selectedModel.specs.forEach { spec in
            spec.values.forEach { value in
                if value.isSelect {
                    selectedSpecValueIds.append(value.id)
                }
            }
        }

        // 查找匹配的SKU并返回库存
        if let matchedSku = selectedModel.skus.first(where: { $0.spec_value_ids == selectedSpecValueIds }) {
            return matchedSku.stock
        }

        return 0
    }

    /// 获取当前选中的所有属性的name值拼接成字符串用逗号分隔
    /// - Returns: 当前选中规格的名称字符串，格式为"规格名:规格值,规格名:规格值"，如果没有选中则返回空字符串
    func getCurrentSelectedSpecNames() -> String {
        guard let selectedModel = self.variants.data.filter({ $0.isSelect }).first else {
            return ""
        }

        var specNames = [selectedModel.name]
        selectedModel.specs.forEach { spec in
            spec.values.forEach { value in
                if value.isSelect {
                    specNames.append("\(value.value)")
                }
            }
        }

        return specNames.joined(separator: ",")
    }

    /// 获取当前选择的SKU ID
    /// - Returns: 当前选中规格对应的SKU ID，如果没有选中或找不到对应SKU则返回空字符串
    func getCurrentSkuId() -> String {
        guard let selectedModel = self.variants.data.filter({ $0.isSelect }).first else {
            return ""
        }

        // 获取当前选中的规格值ID数组
        var selectedSpecValueIds = [String]()
        selectedModel.specs.forEach { spec in
            spec.values.forEach { value in
                if value.isSelect {
                    selectedSpecValueIds.append(value.id)
                }
            }
        }

        // 查找匹配的SKU并返回SKU ID
        if let matchedSku = selectedModel.skus.first(where: { $0.spec_value_ids == selectedSpecValueIds }) {
            return matchedSku.sku_id
        }

        return ""
    }

    /// 获取当前选择的SKU对象
    /// - Returns: 当前选中规格对应的SKU对象，如果没有选中或找不到对应SKU则返回nil
    func getSelectedSku() -> ProductDetailSku? {
        guard let selectedModel = self.variants.data.filter({ $0.isSelect }).first else {
            return nil
        }

        // 获取当前选中的规格值ID数组
        var selectedSpecValueIds = [String]()
        selectedModel.specs.forEach { spec in
            spec.values.forEach { value in
                if value.isSelect {
                    selectedSpecValueIds.append(value.id)
                }
            }
        }

        // 查找匹配的SKU并返回SKU对象
        return selectedModel.skus.first(where: { $0.spec_value_ids == selectedSpecValueIds })
    }
}
/// 商品基本信息模型
struct ProductDetailVariantData: SmartCodable {
    var title: String = ""
    var data: [ProductDetailVariant] = []
}
/// 商品基本信息模型
struct ProductDetailProduct: SmartCodable {
    var is_available: Bool = false
    var location: String = ""
    var recent_reviews: [CommentModel] = []
    var status: String = ""
    var is_type: String = ""
    var average_rating: Int = 0
    var img_urls: [String] = []
    var updated_at: String = ""
    var recommend_list: [AdDetailSkuItem] = []
    var category: String = ""
    var name: String = ""
    var pay_channel: [String] = []
    var detail_images: String = ""
    var id: String = ""
    var reviews_count: Int = 0
    var view: String = ""
    var address_list: [ProductDetailLocation] = []
    var created_at: String = ""
    var seller_id: String = ""
    var cover_image: String = ""
    var description: String = ""
    var postage_type_text: String = ""
    /// 邮寄方式1为包邮2为不包邮
    var postage_type:Int = 1
    /// 邮费
    var postage_fee: String = "0"
}
struct ProductDetailLocation: SmartCodable {
    var id: String = ""
    var user_id: String = ""
    var recipient_name: String = ""
    var phone: String = ""
    var province: String = ""
    var city: String = ""
    var district: String = ""
    var detail: String = ""
    var postal_code: String = ""
    var is_default: String = ""
    var created_at: String = ""
    var is_deleted: String = ""
  
}

/// 推荐商品模型
struct ProductDetailRecommend: SmartCodable {
    var id: String = ""
    var category: String = ""
    var description: String = ""
    var thumb: String = ""
    var created_at: String = ""
    var cover_image: String = ""
    var detail_images: String = ""
    var image_urls: [String] = []
    var location: String = ""
    var is_type: String = ""
    var updated_at: String = ""
    var postage_type: String = ""
    var view: String = ""
    var seller_id: String = ""
    var name: String = ""
    var status: String = ""
}

/// 商品规格模型
struct ProductDetailSpec: SmartCodable {
    var spec_id: String = ""
    var name: String = ""
    var values: [ProductDetailSpecValue] = []
}

/// 规格值模型
struct ProductDetailSpecValue: SmartCodable {
    var id: String = ""
    var spec_id: String = ""
    var value: String = ""
    var created_at: String = ""
    var isSelect = false
    var selectable = false
}

/// 商品变体模型
struct ProductDetailVariant: SmartCodable {
    var available_spec_values: [ProductDetailAvailableSpecValue] = []
    var id: String = ""
    var name: String = ""
    var cover_image: String = ""
    var images: [String] = []
    var skus: [ProductDetailSku] = []
    var specs: [ProductDetailSpec] = []
    var isSelect = false
}

/// 可用规格值模型
struct ProductDetailAvailableSpecValue: SmartCodable {
    var spec_id: Int = 0
    var spec_name: String = ""
    var value_ids: [Int] = []
}

/// SKU模型
struct ProductDetailSku: SmartCodable {
    var sku_id: String = ""
    var spec_value_ids: [String] = []
    var sku_code: String = ""
    var price: String = ""
    var spec_values: [String] = []
    var stock: Int = 0
    var origin_price: String = ""
}

//
//  RegionListItemModel.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/17.
//
struct ProductAddressModel: SmartCodable {
    var address_list: [ProductAddressItemModel] = [ProductAddressItemModel]()

    // 新增方法：获取选中的地址项
    func selectedAddress() -> ProductAddressItemModel? {
        // 查找 address_list 中 isSelect 为 true 的项
        if let selectedItem = address_list.first(where: { $0.isSelect }) {
            return selectedItem
        }

        // 如果都没有选中，返回nil（不自动选择默认地址）
        return nil
    }

    /// 设置默认选择，选中默认地址或第一个地址
    /// - Returns: 设置了默认选项的ProductAddressModel
    mutating func setDefaultSelection() -> ProductAddressModel {
        // 先清除所有选中状态
        for i in 0..<address_list.count {
            address_list[i].isSelect = false
        }

        // 如果有默认地址，选中默认地址
        if let defaultIndex = address_list.firstIndex(where: { $0.is_default }) {
            address_list[defaultIndex].isSelect = true
        } else if !address_list.isEmpty {
            // 如果没有默认地址，选中第一个地址
            address_list[0].isSelect = true
        }

        return self
    }

    /// 设置默认选择（仅在没有任何选中状态时）
    /// - Returns: 设置了默认选项的ProductAddressModel
    mutating func setDefaultSelectionIfNeeded() -> ProductAddressModel {
        // 检查是否已有选中的地址
        let hasSelectedAddress = address_list.contains { $0.isSelect }

        // 如果没有选中的地址，则设置默认选择
        if !hasSelectedAddress {
            return setDefaultSelection()
        }

        return self
    }
}

// MARK: - 创建地址响应模型
struct CreateAddressResponse: SmartCodable {
    var address_id: Int = 0
}
struct ProductAddressItemModel: SmartCodable {
    // 原字段
    var id: String = ""
    var user_id: String = ""
    var recipient_name: String = ""   // 收件人姓名，对应JSON中的"recipient_name"
    var phone: String = ""
    var detail: String = ""
    var is_default: Bool = false
    var isSelect: Bool = false   // 注意：这个可能是本地使用，不在JSON中

    // 新字段
    var city_code: String = ""
    var district_code: String = ""
    var street_code: String = ""
    var created_at: String = ""
    var province: String = ""
    var street: String = ""
    var type: String = ""
    var city: String = ""
    var district: String = ""
    // 注意：JSON中有"recipient_name"，而我们的name就是收件人姓名，所以我们需要将"recipient_name"映射到name
    var postal_code: String = ""
    var province_code: String = ""

    // 提供一个计算属性，返回完整的地区字符串（省市区街道）
    var region: String {
        return province + city + district + street
    }

    /// 选中当前地址项，并清除其他地址项的选中状态
    /// - Parameter addressModel: 包含所有地址的模型
    /// - Returns: 更新后的地址模型
    static func selectAddress(id: String, in addressModel: inout ProductAddressModel) {
        // 清除所有选中状态
        for i in 0..<addressModel.address_list.count {
            addressModel.address_list[i].isSelect = false
        }

        // 设置指定ID的地址为选中状态
        if let index = addressModel.address_list.firstIndex(where: { $0.id == id }) {
            addressModel.address_list[index].isSelect = true
        }
    }

    /// 选中当前地址项（通过索引）
    /// - Parameters:
    ///   - index: 地址在列表中的索引
    ///   - addressModel: 包含所有地址的模型
    static func selectAddress(at index: Int, in addressModel: inout ProductAddressModel) {
        // 清除所有选中状态
        for i in 0..<addressModel.address_list.count {
            addressModel.address_list[i].isSelect = false
        }

        // 设置指定索引的地址为选中状态
        if index >= 0 && index < addressModel.address_list.count {
            addressModel.address_list[index].isSelect = true
        }
    }
}

struct RegionListItemModel: SmartCodable {
    var code: String = ""
    var name: String = ""
    var parent_code: String = ""
    var level: String = ""
    var has_children: Bool = false
    var isSelected: Bool = false
}

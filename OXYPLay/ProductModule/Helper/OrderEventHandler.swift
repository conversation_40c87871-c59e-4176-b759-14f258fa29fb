//
//  OrderEventHandler.swift
//  OXYPLay
//
//  Created by Renhw on 2025/8/5.
//

import Foundation
import Combine
import UIKit

/// 订单事件处理器协议
protocol OrderEventHandlerDelegate: AnyObject {
    /// 需要跳转到支付页面
    /// - Parameter orderId: 订单ID
    func orderEventHandler(_ handler: OrderEventHandler, needsPaymentForOrder orderId: Int)
    
    /// 需要跳转到发货页面
    /// - Parameter orderId: 订单ID
    func orderEventHandler(_ handler: OrderEventHandler, needsShippingForOrder orderId: Int)
    
    /// 需要跳转到评价页面
    /// - Parameters:
    ///   - orderId: 订单ID
    ///   - productId: 商品ID
    ///   - specValueText: 规格文本
    func orderEventHandler(_ handler: OrderEventHandler, needsReviewForOrder orderId: Int, productId: Int, specValueText: String)
    
    /// 需要跳转到查看评价页面
    /// - Parameter orderId: 订单ID
    func orderEventHandler(_ handler: OrderEventHandler, needsViewReviewForOrder orderId: Int)
    
    /// 需要跳转到物流详情页面
    /// - Parameter orderId: 订单ID
    func orderEventHandler(_ handler: OrderEventHandler, needsViewLogisticsForOrder orderId: Int)
    
    /// 需要跳转到退货发货页面
    /// - Parameter orderId: 订单ID
    func orderEventHandler(_ handler: OrderEventHandler, needsReturnGoodsForOrder orderId: Int)
    
    /// 需要处理申请退款流程
    /// - Parameter orderId: 订单ID
    func orderEventHandler(_ handler: OrderEventHandler, needsApplyRefundForOrder orderId: Int)
    
    /// 需要处理同意退款流程
    /// - Parameter orderId: 订单ID
    func orderEventHandler(_ handler: OrderEventHandler, needsAgreeRefundForOrder orderId: Int)
    
    /// 订单状态已更新，需要刷新页面
    /// - Parameter orderId: 订单ID
    func orderEventHandler(_ handler: OrderEventHandler, orderDidUpdate orderId: Int)
    
    /// 显示成功消息
    /// - Parameter message: 成功消息
    func orderEventHandler(_ handler: OrderEventHandler, showSuccessMessage message: String)
    
    /// 显示错误消息
    /// - Parameter message: 错误消息
    func orderEventHandler(_ handler: OrderEventHandler, showErrorMessage message: String)
}

/// 订单事件处理器
/// 统一处理订单相关的事件，包括网络请求和UI跳转
class OrderEventHandler {
    
    // MARK: - Properties
    
    /// 代理对象
    weak var delegate: OrderEventHandlerDelegate?
    
    /// 订单事件ViewModel
    private let eventViewModel = OrderEventViewModel()
    
    /// 订阅集合
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    
    init() {
        setupBindings()
    }
    
    // MARK: - Setup Methods
    
    /// 设置数据绑定
    private func setupBindings() {
        // 监听事件处理结果
        eventViewModel.$eventResult
            .receive(on: DispatchQueue.main)
            .sink { [weak self] result in
                guard let self = self, let result = result else { return }
                
                switch result {
                case .success(let message):
                    self.delegate?.orderEventHandler(self, showSuccessMessage: message)
                case .failure(let error):
                    self.delegate?.orderEventHandler(self, showErrorMessage: error)
                }
            }
            .store(in: &cancellables)
        
        // 监听订单更新通知
        eventViewModel.orderUpdatedPublisher
            .receive(on: DispatchQueue.main)
            .sink { [weak self] orderId in
                guard let self = self else { return }
                self.delegate?.orderEventHandler(self, orderDidUpdate: orderId)
            }
            .store(in: &cancellables)
        
        // 监听支付成功通知
        eventViewModel.paymentSuccessPublisher
            .receive(on: DispatchQueue.main)
            .sink { [weak self] orderId in
                guard let self = self else { return }
                self.delegate?.orderEventHandler(self, needsPaymentForOrder: orderId)
            }
            .store(in: &cancellables)
        
        // 监听退款操作成功通知
        eventViewModel.refundOperationSuccessPublisher
            .receive(on: DispatchQueue.main)
            .sink { [weak self] (orderId, message) in
                guard let self = self else { return }
                self.delegate?.orderEventHandler(self, showSuccessMessage: message)
                self.delegate?.orderEventHandler(self, orderDidUpdate: orderId)
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Public Methods
    
    /// 处理订单按钮点击事件
    /// - Parameters:
    ///   - action: 按钮操作类型
    ///   - order: 订单模型
    func handleOrderAction(_ action: String, order: ProductOrderModel) {
        let orderId = order.order_id
        
        switch action {
        case OrderEventAction.cancel.rawValue, OrderEventAction.cancelOrder.rawValue:
            eventViewModel.handleOrderEvent(.cancel(orderId: orderId))
            
        case OrderEventAction.pay.rawValue:
            delegate?.orderEventHandler(self, needsPaymentForOrder: orderId)
            
        case OrderEventAction.ship.rawValue:
            delegate?.orderEventHandler(self, needsShippingForOrder: orderId)
            
        case OrderEventAction.confirm.rawValue:
            eventViewModel.handleOrderEvent(.confirm(orderId: orderId))
            
        case OrderEventAction.review.rawValue:
            // 需要从订单中获取商品信息
            if let firstItem = order.firstItem {
                delegate?.orderEventHandler(self, needsReviewForOrder: orderId, productId: firstItem.id, specValueText: firstItem.spec_value_text)
            }
            
        case OrderEventAction.viewReview.rawValue:
            delegate?.orderEventHandler(self, needsViewReviewForOrder: orderId)
            
        case OrderEventAction.viewLogistics.rawValue:
            delegate?.orderEventHandler(self, needsViewLogisticsForOrder: orderId)
            
        case OrderEventAction.applyRefund.rawValue:
            delegate?.orderEventHandler(self, needsApplyRefundForOrder: orderId)
            
        case OrderEventAction.cancelRefund.rawValue:
            eventViewModel.handleOrderEvent(.cancelRefund(orderId: orderId))
            
        case OrderEventAction.agreeRefund.rawValue:
            delegate?.orderEventHandler(self, needsAgreeRefundForOrder: orderId)
            
        case OrderEventAction.rejectRefund.rawValue:
            // 可以弹窗让用户输入拒绝理由
            showRejectRefundDialog(orderId: orderId)
            
        case OrderEventAction.returnGoods.rawValue:
            delegate?.orderEventHandler(self, needsReturnGoodsForOrder: orderId)
            
        case OrderEventAction.rejectReturnGoods.rawValue:
            // 可以弹窗让用户输入拒收备注
            showRejectReturnDialog(orderId: orderId)
            
        case OrderEventAction.confirmReceiveAndRefund.rawValue:
            eventViewModel.handleOrderEvent(.confirmReturnRefund(orderId: orderId))
            
        default:
            print("未处理的订单操作: \(action)")
        }
    }
    
    /// 处理订单详情页面的按钮点击事件
    /// - Parameters:
    ///   - action: 按钮操作类型
    ///   - orderDetail: 订单详情模型
    func handleOrderDetailAction(_ action: String, orderDetail: OrderDetailModel) {
        let orderId = orderDetail.order_id
        
        switch action {
        case OrderEventAction.cancel.rawValue, OrderEventAction.cancelOrder.rawValue:
            eventViewModel.handleOrderEvent(.cancel(orderId: orderId))
            
        case OrderEventAction.pay.rawValue:
            delegate?.orderEventHandler(self, needsPaymentForOrder: orderId)
            
        case OrderEventAction.ship.rawValue:
            delegate?.orderEventHandler(self, needsShippingForOrder: orderId)
            
        case OrderEventAction.confirm.rawValue:
            eventViewModel.handleOrderEvent(.confirm(orderId: orderId))
            
        case OrderEventAction.review.rawValue:
            // 需要从订单详情中获取商品信息
            if let firstItem = orderDetail.items.first {
                delegate?.orderEventHandler(self, needsReviewForOrder: orderId, productId: firstItem.product_id, specValueText: firstItem.spec_value_text)
            }
            
        case OrderEventAction.viewReview.rawValue:
            delegate?.orderEventHandler(self, needsViewReviewForOrder: orderId)
            
        case OrderEventAction.viewLogistics.rawValue:
            delegate?.orderEventHandler(self, needsViewLogisticsForOrder: orderId)
            
        case OrderEventAction.applyRefund.rawValue:
            delegate?.orderEventHandler(self, needsApplyRefundForOrder: orderId)
            
        case OrderEventAction.cancelRefund.rawValue:
            eventViewModel.handleOrderEvent(.cancelRefund(orderId: orderId))
            
        case OrderEventAction.agreeRefund.rawValue:
            delegate?.orderEventHandler(self, needsAgreeRefundForOrder: orderId)
            
        case OrderEventAction.rejectRefund.rawValue:
            showRejectRefundDialog(orderId: orderId)
            
        case OrderEventAction.returnGoods.rawValue:
            delegate?.orderEventHandler(self, needsReturnGoodsForOrder: orderId)
            
        case OrderEventAction.rejectReturnGoods.rawValue:
            showRejectReturnDialog(orderId: orderId)
            
        case OrderEventAction.confirmReceiveAndRefund.rawValue:
            eventViewModel.handleOrderEvent(.confirmReturnRefund(orderId: orderId))
            
        default:
            print("未处理的订单操作: \(action)")
        }
    }
    
    /// 申请退款
    /// - Parameters:
    ///   - orderId: 订单ID
    ///   - params: 退款参数
    func applyRefund(orderId: Int, params: [String: Any]) {
        eventViewModel.handleOrderEvent(.applyRefund(orderId: orderId, params: params))
    }
    
    /// 同意退款
    /// - Parameters:
    ///   - orderId: 订单ID
    ///   - addressId: 退货地址ID（可选）
    ///   - addressSnapshot: 地址快照（可选）
    func agreeRefund(orderId: Int, addressId: Int? = nil, addressSnapshot: String? = nil) {
        eventViewModel.handleOrderEvent(.agreeRefund(orderId: orderId, addressId: addressId, addressSnapshot: addressSnapshot))
    }
    
    // MARK: - Private Methods
    
    /// 显示拒绝退款对话框
    /// - Parameter orderId: 订单ID
    private func showRejectRefundDialog(orderId: Int) {
        // TODO: 实现拒绝退款对话框
        // 这里可以弹出一个输入框让用户输入拒绝理由
        eventViewModel.handleOrderEvent(.rejectRefund(orderId: orderId, reason: "卖家拒绝退款"))
    }
    
    /// 显示拒绝退货对话框
    /// - Parameter orderId: 订单ID
    private func showRejectReturnDialog(orderId: Int) {
        // TODO: 实现拒绝退货对话框
        // 这里可以弹出一个输入框让用户输入拒收备注
        eventViewModel.handleOrderEvent(.rejectReturn(orderId: orderId, remark: "卖家拒绝收货"))
    }
    
    /// 重置事件状态
    func resetEventState() {
        eventViewModel.resetEventResult()
    }
    
    // MARK: - Deinit
    
    deinit {
        cancellables.removeAll()
    }
}

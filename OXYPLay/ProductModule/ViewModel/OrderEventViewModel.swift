//
//  OrderEventViewModel.swift
//  OXYPLay
//
//  Created by Renhw on 2025/8/5.
//

import Foundation
import Combine
import UIKit

/// 订单事件类型
enum OrderEventType {
    case cancel(orderId: Int)                                    // 取消订单
    case pay(orderId: Int)                                       // 支付订单
    case ship(orderId: Int)                                      // 发货
    case confirm(orderId: Int)                                   // 确认收货
    case review(orderId: Int, productId: Int, specValueText: String) // 评价
    case viewReview(orderId: Int)                               // 查看评价
    case viewLogistics(orderId: Int)                            // 查看物流
    case applyRefund(orderId: Int, params: [String: Any])       // 申请退款
    case cancelRefund(orderId: Int)                             // 取消退款申请
    case agreeRefund(orderId: Int, addressId: Int?, addressSnapshot: String?) // 同意退款
    case rejectRefund(orderId: Int, reason: String?)            // 拒绝退款
    case rejectReturn(orderId: Int, remark: String?)            // 拒绝退货
    case confirmReturnRefund(orderId: Int)                      // 确认收货并退款
    case returnGoods(orderId: Int)                              // 退货发货
}

/// 订单事件结果
enum OrderEventResult {
    case success(message: String)
    case failure(error: String)
}

/// 订单事件处理ViewModel
/// 专门处理各种订单操作的网络请求和业务逻辑
class OrderEventViewModel: BaseViewModel {
    
    // MARK: - Published Properties
    
    /// 事件处理结果
    @Published var eventResult: OrderEventResult?
    
    /// 操作成功消息
    @Published var successMessage: String?
    
    /// 操作失败消息
    @Published var errorMessage: String?
    
    // MARK: - Publishers
    
    /// 订单状态更新通知
    /// 当订单操作成功后，发布订单ID通知其他页面刷新
    let orderUpdatedPublisher = PassthroughSubject<Int, Never>()
    
    /// 支付成功通知
    let paymentSuccessPublisher = PassthroughSubject<Int, Never>()
    
    /// 退款相关操作成功通知
    let refundOperationSuccessPublisher = PassthroughSubject<(Int, String), Never>()
    
    // MARK: - Public Methods
    
    /// 处理订单事件
    /// - Parameter event: 订单事件类型
    func handleOrderEvent(_ event: OrderEventType) {
        switch event {
        case .cancel(let orderId):
            cancelOrder(orderId: orderId)
        case .pay(let orderId):
            // 支付操作通常需要跳转到支付页面，这里只是示例
            handlePayment(orderId: orderId)
        case .ship(let orderId):
            handleShipping(orderId: orderId)
        case .confirm(let orderId):
            confirmReceive(orderId: orderId)
        case .review(let orderId, let productId, let specValueText):
            handleReview(orderId: orderId, productId: productId, specValueText: specValueText)
        case .viewReview(let orderId):
            handleViewReview(orderId: orderId)
        case .viewLogistics(let orderId):
            handleViewLogistics(orderId: orderId)
        case .applyRefund(let orderId, let params):
            applyRefund(orderId: orderId, params: params)
        case .cancelRefund(let orderId):
            cancelRefund(orderId: orderId)
        case .agreeRefund(let orderId, let addressId, let addressSnapshot):
            agreeRefund(orderId: orderId, addressId: addressId, addressSnapshot: addressSnapshot)
        case .rejectRefund(let orderId, let reason):
            rejectRefund(orderId: orderId, reason: reason)
        case .rejectReturn(let orderId, let remark):
            rejectReturn(orderId: orderId, remark: remark)
        case .confirmReturnRefund(let orderId):
            confirmReturnRefund(orderId: orderId)
        case .returnGoods(let orderId):
            handleReturnGoods(orderId: orderId)
        }
    }
    
    // MARK: - Network Request Methods
    
    /// 取消订单
    /// - Parameter orderId: 订单ID
    private func cancelOrder(orderId: Int) {
        let params = RequestParameters([
            "order_id": orderId
        ])
        
        requestModel(OrderService.cancel(params: params), type: ResponseModel.self)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    if case .failure(let error) = completion {
                        self?.handleError(error, operation: "取消订单")
                        self?.eventResult = .failure(error: error.localizedDescription)
                        self?.errorMessage = error.localizedDescription
                    }
                },
                receiveValue: { [weak self] response in
                    self?.eventResult = .success(message: response.message)
                    self?.successMessage = response.message
                    self?.orderUpdatedPublisher.send(orderId)
                }
            )
            .store(in: &cancellables)
    }
    
    /// 申请退款
    /// - Parameters:
    ///   - orderId: 订单ID
    ///   - params: 退款参数
    private func applyRefund(orderId: Int, params: [String: Any]) {
        let requestParams = RequestParameters(params)
        
        requestModel(OrderService.applyRefund(params: requestParams), type: ResponseModel.self)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    if case .failure(let error) = completion {
                        self?.handleError(error, operation: "申请退款")
                        self?.eventResult = .failure(error: error.localizedDescription)
                        self?.errorMessage = error.localizedDescription
                    }
                },
                receiveValue: { [weak self] response in
                    self?.eventResult = .success(message: response.message)
                    self?.successMessage = response.message
                    self?.orderUpdatedPublisher.send(orderId)
                    self?.refundOperationSuccessPublisher.send((orderId, "申请退款成功"))
                }
            )
            .store(in: &cancellables)
    }
    
    /// 取消退款申请
    /// - Parameter orderId: 订单ID
    private func cancelRefund(orderId: Int) {
        let params = RequestParameters([
            "orderId": orderId
        ])
        
        requestModel(OrderService.cancelRefund(params: params), type: ResponseModel.self)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    if case .failure(let error) = completion {
                        self?.handleError(error, operation: "取消退款申请")
                        self?.eventResult = .failure(error: error.localizedDescription)
                        self?.errorMessage = error.localizedDescription
                    }
                },
                receiveValue: { [weak self] response in
                    self?.eventResult = .success(message: response.message)
                    self?.successMessage = response.message
                    self?.orderUpdatedPublisher.send(orderId)
                    self?.refundOperationSuccessPublisher.send((orderId, "取消退款申请成功"))
                }
            )
            .store(in: &cancellables)
    }
    
    /// 同意退款
    /// - Parameters:
    ///   - orderId: 订单ID
    ///   - addressId: 退货地址ID（可选）
    ///   - addressSnapshot: 地址快照（可选）
    private func agreeRefund(orderId: Int, addressId: Int?, addressSnapshot: String?) {
        var params: [String: Any] = [
            "order_id": orderId
        ]
        
        if let addressId = addressId {
            params["address_id"] = addressId
        }
        
        if let addressSnapshot = addressSnapshot {
            params["address_snapshot"] = addressSnapshot
        }
        
        let requestParams = RequestParameters(params)
        
        requestModel(OrderService.agreeRefund(params: requestParams), type: ResponseModel.self)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    if case .failure(let error) = completion {
                        self?.handleError(error, operation: "同意退款")
                        self?.eventResult = .failure(error: error.localizedDescription)
                        self?.errorMessage = error.localizedDescription
                    }
                },
                receiveValue: { [weak self] response in
                    self?.eventResult = .success(message: response.message)
                    self?.successMessage = response.message
                    self?.orderUpdatedPublisher.send(orderId)
                    self?.refundOperationSuccessPublisher.send((orderId, "同意退款成功"))
                }
            )
            .store(in: &cancellables)
    }
    
    /// 拒绝退款申请
    /// - Parameters:
    ///   - orderId: 订单ID
    ///   - reason: 拒绝理由（可选）
    private func rejectRefund(orderId: Int, reason: String?) {
        var params: [String: Any] = [
            "orderId": orderId
        ]
        
        if let reason = reason {
            params["reject_reason"] = reason
        }
        
        let requestParams = RequestParameters(params)
        
        requestModel(OrderService.rejectRefund(params: requestParams), type: ResponseModel.self)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    if case .failure(let error) = completion {
                        self?.handleError(error, operation: "拒绝退款")
                        self?.eventResult = .failure(error: error.localizedDescription)
                        self?.errorMessage = error.localizedDescription
                    }
                },
                receiveValue: { [weak self] response in
                    self?.eventResult = .success(message: response.message)
                    self?.successMessage = response.message
                    self?.orderUpdatedPublisher.send(orderId)
                    self?.refundOperationSuccessPublisher.send((orderId, "拒绝退款成功"))
                }
            )
            .store(in: &cancellables)
    }
    
    /// 拒绝退货（终止退货流程）
    /// - Parameters:
    ///   - orderId: 订单ID
    ///   - remark: 拒收备注（可选）
    private func rejectReturn(orderId: Int, remark: String?) {
        var params: [String: Any] = [
            "orderId": orderId
        ]
        
        if let remark = remark {
            params["remark"] = remark
        }
        
        let requestParams = RequestParameters(params)
        
        requestModel(OrderService.rejectReturn(params: requestParams), type: ResponseModel.self)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    if case .failure(let error) = completion {
                        self?.handleError(error, operation: "拒绝退货")
                        self?.eventResult = .failure(error: error.localizedDescription)
                        self?.errorMessage = error.localizedDescription
                    }
                },
                receiveValue: { [weak self] response in
                    self?.eventResult = .success(message: response.message)
                    self?.successMessage = response.message
                    self?.orderUpdatedPublisher.send(orderId)
                    self?.refundOperationSuccessPublisher.send((orderId, "拒绝退货成功"))
                }
            )
            .store(in: &cancellables)
    }
    
    /// 确认收货并退款
    /// - Parameter orderId: 订单ID
    private func confirmReturnRefund(orderId: Int) {
        let params = RequestParameters([
            "orderId": orderId
        ])
        
        requestModel(OrderService.confirmReturnRefund(params: params), type: ResponseModel.self)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    if case .failure(let error) = completion {
                        self?.handleError(error, operation: "确认收货并退款")
                        self?.eventResult = .failure(error: error.localizedDescription)
                        self?.errorMessage = error.localizedDescription
                    }
                },
                receiveValue: { [weak self] response in
                    self?.eventResult = .success(message: response.message)
                    self?.successMessage = response.message
                    self?.orderUpdatedPublisher.send(orderId)
                    self?.refundOperationSuccessPublisher.send((orderId, "确认收货并退款成功"))
                }
            )
            .store(in: &cancellables)
    }

    // MARK: - UI Event Handlers

    /// 处理支付操作
    /// - Parameter orderId: 订单ID
    private func handlePayment(orderId: Int) {
        // 支付操作通常需要跳转到支付页面
        // 这里发布事件，让Controller处理UI跳转
        eventResult = .success(message: "跳转到支付页面")
        paymentSuccessPublisher.send(orderId)
    }

    /// 处理发货操作
    /// - Parameter orderId: 订单ID
    private func handleShipping(orderId: Int) {
        // 发货操作通常需要跳转到发货页面
        eventResult = .success(message: "跳转到发货页面")
        orderUpdatedPublisher.send(orderId)
    }

    /// 确认收货
    /// - Parameter orderId: 订单ID
    private func confirmReceive(orderId: Int) {
        // TODO: 实现确认收货接口
        eventResult = .success(message: "确认收货功能待实现")
        orderUpdatedPublisher.send(orderId)
    }

    /// 处理评价操作
    /// - Parameters:
    ///   - orderId: 订单ID
    ///   - productId: 商品ID
    ///   - specValueText: 规格文本
    private func handleReview(orderId: Int, productId: Int, specValueText: String) {
        // 评价操作通常需要跳转到评价页面
        eventResult = .success(message: "跳转到评价页面")
        orderUpdatedPublisher.send(orderId)
    }

    /// 处理查看评价操作
    /// - Parameter orderId: 订单ID
    private func handleViewReview(orderId: Int) {
        // 查看评价操作通常需要跳转到评价详情页面
        eventResult = .success(message: "跳转到评价详情页面")
    }

    /// 处理查看物流操作
    /// - Parameter orderId: 订单ID
    private func handleViewLogistics(orderId: Int) {
        // 查看物流操作通常需要跳转到物流详情页面
        eventResult = .success(message: "跳转到物流详情页面")
    }

    /// 处理退货发货操作
    /// - Parameter orderId: 订单ID
    private func handleReturnGoods(orderId: Int) {
        // 退货发货操作通常需要跳转到退货发货页面
        eventResult = .success(message: "跳转到退货发货页面")
        orderUpdatedPublisher.send(orderId)
    }

    // MARK: - Utility Methods

    /// 重置事件结果
    func resetEventResult() {
        eventResult = nil
        successMessage = nil
        errorMessage = nil
    }

    /// 根据操作类型获取操作名称
    /// - Parameter event: 订单事件类型
    /// - Returns: 操作名称
    func getEventName(_ event: OrderEventType) -> String {
        switch event {
        case .cancel:
            return "取消订单"
        case .pay:
            return "支付订单"
        case .ship:
            return "发货"
        case .confirm:
            return "确认收货"
        case .review:
            return "评价"
        case .viewReview:
            return "查看评价"
        case .viewLogistics:
            return "查看物流"
        case .applyRefund:
            return "申请退款"
        case .cancelRefund:
            return "取消退款申请"
        case .agreeRefund:
            return "同意退款"
        case .rejectRefund:
            return "拒绝退款"
        case .rejectReturn:
            return "拒绝退货"
        case .confirmReturnRefund:
            return "确认收货并退款"
        case .returnGoods:
            return "退货发货"
        }
    }
}

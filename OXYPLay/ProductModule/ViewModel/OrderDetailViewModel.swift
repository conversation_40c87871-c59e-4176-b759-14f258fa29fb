//
//  OrderDetailViewModel.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/25.
//

import Foundation
import Combine

/// 订单详情ViewModel
class OrderDetailViewModel: BaseViewModel {

    // MARK: - Published Properties

    /// 订单详情数据
    @Published var orderDetail: OrderDetailModel?

    /// 申请退款成功消息
    @Published var refundSuccessMessage: String?

    /// 申请退款失败消息
    @Published var refundErrorMessage: String?

    // MARK: - Private Properties

    private var orderId: Int = 0

    // MARK: - 初始化

    init(orderId: Int) {
        self.orderId = orderId
        super.init()
    }

    // MARK: - 数据请求


    /// 获取订单详情
    func fetchOrderDetail() {
        let params = RequestParameters([
            "order_id": orderId
        ])

        requestModel(OrderService.orderDetail(params: params), type: OrderDetailResponse.self)
            .sink { [weak self] completion in
                if case .failure(let error) = completion {
                    print("加载订单详情失败: \(error.localizedDescription)")
                }
            } receiveValue: { [weak self] response in
                var order = OrderDetailModel()
                order = response.order
                order.status_map = response.status_map
                self?.orderDetail = order
            }
            .store(in: &cancellables)
    }

    override func refreshData() {
        fetchOrderDetail()
    }

    /// 复制文本到剪贴板
    /// - Parameter text: 要复制的文本
    func copyToClipboard(text: String) {
        UIPasteboard.general.string = text
        // TODO: 显示复制成功提示
        print("已复制: \(text)")
    }
    
    // MARK: - Private Methods

    deinit {
        // 清理资源
    }
}
